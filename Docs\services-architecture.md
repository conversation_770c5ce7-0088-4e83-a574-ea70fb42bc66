# Services Architecture Document
## Talmatha Apprenticeship Platform

### Table of Contents
1. [Service Layer Design](#service-layer-design)
2. [Business Services](#business-services)
3. [Infrastructure Services](#infrastructure-services)
4. [Service Communication Patterns](#service-communication-patterns)
5. [Error Handling and Resilience](#error-handling-and-resilience)
6. [Cross-Cutting Concerns](#cross-cutting-concerns)

---

## Service Layer Design

### Service Architecture Overview

The Talmatha platform implements a layered service architecture that separates business logic from infrastructure concerns while maintaining clear boundaries and responsibilities.

```mermaid
graph TB
    subgraph "Service Layers"
        A[Application Services<br/>Orchestration & Coordination] --> B[Domain Services<br/>Business Logic]
        A --> C[Infrastructure Services<br/>External Integrations]
        B --> D[Repository Services<br/>Data Access]
        C --> E[External Systems<br/>APIs, Storage, Email]
    end
    
    subgraph "Cross-Cutting Services"
        F[Logging Service]
        G[Validation Service]
        H[Mapping Service]
        I[Caching Service]
        J[Security Service]
    end
    
    A -.-> F
    A -.-> G
    A -.-> H
    B -.-> I
    C -.-> J
```

### Service Design Principles

#### Single Responsibility Principle
Each service has a single, well-defined responsibility:
- **Domain Services**: Encapsulate business rules and logic
- **Application Services**: Orchestrate use cases and workflows
- **Infrastructure Services**: Handle external system interactions
- **Repository Services**: Manage data persistence and retrieval

#### Dependency Inversion
Services depend on abstractions, not concrete implementations:
- All services implement interfaces
- Dependencies injected through constructor injection
- Enables testability and flexibility

#### Interface Segregation
Services expose only the methods their clients need:
- Focused, cohesive interfaces
- Prevents unnecessary coupling
- Supports independent evolution

---

## Business Services

### Domain Services

#### Application Eligibility Service
Manages business rules for trainee application eligibility.

```csharp
public interface IApplicationEligibilityService
{
    Task<EligibilityResult> CheckEligibilityAsync(int traineeId, int opportunityId);
    Task<bool> CanApplyAsync(int traineeId, int opportunityId);
    Task<bool> CanWithdrawAsync(int applicationId, int traineeId);
    Task<IEnumerable<string>> GetEligibilityReasonsAsync(int traineeId, int opportunityId);
}

public class ApplicationEligibilityService : IApplicationEligibilityService
{
    private readonly IRepositoryManager _repository;
    private readonly ILogger<ApplicationEligibilityService> _logger;

    public async Task<EligibilityResult> CheckEligibilityAsync(int traineeId, int opportunityId)
    {
        var result = new EligibilityResult();
        
        // Check if opportunity exists and is active
        var opportunity = await _repository.TrainingOpportunities.GetByIdAsync(opportunityId);
        if (opportunity == null)
        {
            result.AddReason("Training opportunity not found");
            return result;
        }

        if (opportunity.OpportunityStatus != OpportunityStatus.Approved)
        {
            result.AddReason("Training opportunity is not currently available");
            return result;
        }

        // Check application deadline
        if (opportunity.ApplicationDeadline < DateTime.UtcNow)
        {
            result.AddReason("Application deadline has passed");
            return result;
        }

        // Check for existing application
        var existingApplication = await _repository.Applications
            .GetByCondition(a => a.TraineeId == traineeId && a.OpportunityId == opportunityId)
            .FirstOrDefaultAsync();

        if (existingApplication != null)
        {
            result.AddReason("You have already applied for this opportunity");
            return result;
        }

        // Check trainee profile completeness
        var trainee = await _repository.Trainees.GetByIdAsync(traineeId);
        if (trainee?.User == null)
        {
            result.AddReason("Trainee profile not found");
            return result;
        }

        // Additional business rules can be added here
        result.IsEligible = true;
        return result;
    }
}
```

#### Company Approval Service
Handles the business logic for company approval workflows.

```csharp
public interface ICompanyApprovalService
{
    Task<ApprovalResult> ApproveCompanyAsync(int companyId, int approverId, string comments = null);
    Task<ApprovalResult> RejectCompanyAsync(int companyId, int approverId, string reason);
    Task<bool> CanApproveCompanyAsync(int companyId, int approverId);
    Task<IEnumerable<Company>> GetPendingApprovalsAsync();
}

public class CompanyApprovalService : ICompanyApprovalService
{
    private readonly IRepositoryManager _repository;
    private readonly INotificationService _notificationService;
    private readonly ILogger<CompanyApprovalService> _logger;

    public async Task<ApprovalResult> ApproveCompanyAsync(int companyId, int approverId, string comments = null)
    {
        var company = await _repository.Companies.GetByIdAsync(companyId);
        if (company == null)
            return ApprovalResult.Failed("Company not found");

        if (company.ApprovalStatus != ApprovalStatus.Pending)
            return ApprovalResult.Failed("Company is not in pending status");

        // Update company status
        company.ApprovalStatus = ApprovalStatus.Approved;
        company.RejectionReason = null;
        company.UpdatedBy = approverId;
        company.UpdatedAt = DateTime.UtcNow;

        _repository.Companies.Update(company);
        await _repository.SaveChangesAsync();

        // Create approval audit record
        var auditRecord = new CompanyApprovalAudit
        {
            CompanyId = companyId,
            ApproverId = approverId,
            Action = ApprovalAction.Approved,
            Comments = comments,
            ActionDate = DateTime.UtcNow
        };
        await _repository.CompanyApprovalAudits.AddAsync(auditRecord);
        await _repository.SaveChangesAsync();

        // Send notification
        await _notificationService.NotifyCompanyApprovalAsync(companyId, true);

        _logger.LogInformation("Company {CompanyId} approved by user {ApproverId}", companyId, approverId);
        return ApprovalResult.Success("Company approved successfully");
    }

    public async Task<ApprovalResult> RejectCompanyAsync(int companyId, int approverId, string reason)
    {
        if (string.IsNullOrWhiteSpace(reason))
            return ApprovalResult.Failed("Rejection reason is required");

        var company = await _repository.Companies.GetByIdAsync(companyId);
        if (company == null)
            return ApprovalResult.Failed("Company not found");

        if (company.ApprovalStatus != ApprovalStatus.Pending)
            return ApprovalResult.Failed("Company is not in pending status");

        // Update company status
        company.ApprovalStatus = ApprovalStatus.Rejected;
        company.RejectionReason = reason;
        company.UpdatedBy = approverId;
        company.UpdatedAt = DateTime.UtcNow;

        _repository.Companies.Update(company);
        await _repository.SaveChangesAsync();

        // Create approval audit record
        var auditRecord = new CompanyApprovalAudit
        {
            CompanyId = companyId,
            ApproverId = approverId,
            Action = ApprovalAction.Rejected,
            Comments = reason,
            ActionDate = DateTime.UtcNow
        };
        await _repository.CompanyApprovalAudits.AddAsync(auditRecord);
        await _repository.SaveChangesAsync();

        // Send notification
        await _notificationService.NotifyCompanyApprovalAsync(companyId, false, reason);

        _logger.LogInformation("Company {CompanyId} rejected by user {ApproverId} with reason: {Reason}", 
            companyId, approverId, reason);
        return ApprovalResult.Success("Company rejected successfully");
    }
}
```

#### Training Opportunity Management Service
Manages the lifecycle of training opportunities.

```csharp
public interface ITrainingOpportunityService
{
    Task<OpportunityResult> CreateOpportunityAsync(CreateTrainingOpportunityCommand command, int companyId);
    Task<OpportunityResult> UpdateOpportunityAsync(UpdateTrainingOpportunityCommand command);
    Task<OpportunityResult> ApproveOpportunityAsync(int opportunityId, int approverId);
    Task<OpportunityResult> RejectOpportunityAsync(int opportunityId, int approverId, string reason);
    Task<bool> CanEditOpportunityAsync(int opportunityId, int userId);
    Task<IEnumerable<TrainingOpportunity>> GetOpportunitiesNearingDeadlineAsync();
}

public class TrainingOpportunityService : ITrainingOpportunityService
{
    private readonly IRepositoryManager _repository;
    private readonly IFileStorageService _fileStorage;
    private readonly INotificationService _notificationService;
    private readonly IMapper _mapper;

    public async Task<OpportunityResult> CreateOpportunityAsync(CreateTrainingOpportunityCommand command, int companyId)
    {
        // Validate company can create opportunities
        var company = await _repository.Companies.GetByIdAsync(companyId);
        if (company?.ApprovalStatus != ApprovalStatus.Approved)
            return OpportunityResult.Failed("Company is not approved to create opportunities");

        // Create opportunity entity
        var opportunity = _mapper.Map<TrainingOpportunity>(command);
        opportunity.CompanyId = companyId;
        opportunity.OpportunityStatus = OpportunityStatus.PendingApproval;

        // Handle file uploads
        if (command.Attachments?.Any() == true)
        {
            var attachmentUrls = new List<string>();
            foreach (var file in command.Attachments)
            {
                var url = await _fileStorage.UploadFileAsync(file, "opportunity-attachments");
                attachmentUrls.Add(url);
            }
            // Store attachment URLs in opportunity
        }

        var result = await _repository.TrainingOpportunities.AddAsync(opportunity);
        await _repository.SaveChangesAsync();

        // Notify supervisors for approval
        await _notificationService.NotifyOpportunitySubmittedAsync(result.Id);

        return OpportunityResult.Success(result.Id, "Training opportunity created and submitted for approval");
    }
}
```

---

## Infrastructure Services

### File Storage Service

```csharp
public interface IFileStorageService
{
    Task<string> UploadFileAsync(IFormFile file, string containerName, string fileName = null);
    Task<bool> DeleteFileAsync(string fileUrl);
    Task<Stream> DownloadFileAsync(string fileUrl);
    Task<bool> FileExistsAsync(string fileUrl);
    string GetFileUrl(string containerName, string fileName);
    Task<IEnumerable<string>> UploadMultipleFilesAsync(IEnumerable<IFormFile> files, string containerName);
}

public class AzureBlobStorageService : IFileStorageService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<AzureBlobStorageService> _logger;

    public async Task<string> UploadFileAsync(IFormFile file, string containerName, string fileName = null)
    {
        try
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is empty or null");

            fileName ??= GenerateUniqueFileName(file.FileName);
            
            var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
            await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);
            
            var blobClient = containerClient.GetBlobClient(fileName);
            
            // Set content type and metadata
            var blobHttpHeaders = new BlobHttpHeaders
            {
                ContentType = file.ContentType
            };
            
            var metadata = new Dictionary<string, string>
            {
                ["OriginalFileName"] = file.FileName,
                ["UploadedAt"] = DateTime.UtcNow.ToString("O"),
                ["FileSize"] = file.Length.ToString()
            };

            using var stream = file.OpenReadStream();
            await blobClient.UploadAsync(stream, new BlobUploadOptions
            {
                HttpHeaders = blobHttpHeaders,
                Metadata = metadata
            });
            
            _logger.LogInformation("File uploaded successfully: {FileName} to container {ContainerName}", 
                fileName, containerName);
            
            return blobClient.Uri.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file {FileName} to container {ContainerName}", 
                file?.FileName, containerName);
            throw;
        }
    }

    private string GenerateUniqueFileName(string originalFileName)
    {
        var extension = Path.GetExtension(originalFileName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
        var guid = Guid.NewGuid().ToString("N")[..8];
        return $"{timestamp}_{guid}{extension}";
    }
}
```

### Email Service

```csharp
public interface IEmailService
{
    Task SendEmailAsync(EmailMessage message);
    Task SendBulkEmailAsync(IEnumerable<EmailMessage> messages);
    Task SendTemplateEmailAsync(string to, string templateId, object templateData);
    Task<bool> ValidateEmailAddressAsync(string email);
}

public class EmailService : IEmailService
{
    private readonly SmtpClient _smtpClient;
    private readonly IConfiguration _configuration;
    private readonly ITemplateService _templateService;
    private readonly ILogger<EmailService> _logger;

    public async Task SendEmailAsync(EmailMessage message)
    {
        try
        {
            var mailMessage = new MailMessage
            {
                From = new MailAddress(_configuration["Email:FromAddress"], _configuration["Email:FromName"]),
                Subject = message.Subject,
                Body = message.Body,
                IsBodyHtml = message.IsHtml
            };

            foreach (var recipient in message.Recipients)
            {
                mailMessage.To.Add(recipient);
            }

            if (message.Attachments?.Any() == true)
            {
                foreach (var attachment in message.Attachments)
                {
                    mailMessage.Attachments.Add(new Attachment(attachment.Stream, attachment.FileName, attachment.ContentType));
                }
            }

            await _smtpClient.SendMailAsync(mailMessage);
            
            _logger.LogInformation("Email sent successfully to {Recipients}", 
                string.Join(", ", message.Recipients));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Recipients}", 
                string.Join(", ", message.Recipients));
            throw;
        }
    }

    public async Task SendTemplateEmailAsync(string to, string templateId, object templateData)
    {
        var template = await _templateService.GetTemplateAsync(templateId);
        if (template == null)
            throw new InvalidOperationException($"Email template '{templateId}' not found");

        var subject = await _templateService.RenderTemplateAsync(template.SubjectTemplate, templateData);
        var body = await _templateService.RenderTemplateAsync(template.BodyTemplate, templateData);

        var message = new EmailMessage
        {
            Recipients = new[] { to },
            Subject = subject,
            Body = body,
            IsHtml = template.IsHtml
        };

        await SendEmailAsync(message);
    }
}
```

### Notification Service

```csharp
public interface INotificationService
{
    Task NotifyTraineeRegistrationAsync(int traineeId);
    Task NotifyCompanyRegistrationAsync(int companyId);
    Task NotifyCompanyApprovalAsync(int companyId, bool approved, string reason = null);
    Task NotifyApplicationSubmittedAsync(int applicationId);
    Task NotifyApplicationStatusChangedAsync(int applicationId, ApplicationStatus newStatus);
    Task NotifyOpportunitySubmittedAsync(int opportunityId);
    Task NotifyOpportunityApprovedAsync(int opportunityId);
    Task NotifyDeadlineReminderAsync(int opportunityId, int daysRemaining);
}

public class NotificationService : INotificationService
{
    private readonly IEmailService _emailService;
    private readonly IRepositoryManager _repository;
    private readonly ILogger<NotificationService> _logger;

    public async Task NotifyApplicationSubmittedAsync(int applicationId)
    {
        var application = await _repository.Applications.GetApplicationWithDetailsAsync(applicationId);
        if (application == null)
        {
            _logger.LogWarning("Application {ApplicationId} not found for notification", applicationId);
            return;
        }

        // Notify trainee
        await _emailService.SendTemplateEmailAsync(
            application.Trainee.User.Email,
            "application-submitted-confirmation",
            new 
            { 
                TraineeName = application.Trainee.User.FullName,
                OpportunityTitle = application.Opportunity.TitleEn,
                CompanyName = application.Opportunity.Company.CompanyNameEn,
                ApplicationDate = application.ApplicationDate.ToString("dd/MM/yyyy"),
                ApplicationId = application.Id
            });

        // Notify company representatives
        var representatives = await _repository.CompanyRepresentatives
            .GetByCondition(cr => cr.CompanyId == application.Opportunity.CompanyId)
            .Include(cr => cr.User)
            .ToListAsync();

        foreach (var representative in representatives)
        {
            await _emailService.SendTemplateEmailAsync(
                representative.User.Email,
                "new-application-received",
                new 
                { 
                    RepresentativeName = representative.User.FullName,
                    TraineeName = application.Trainee.User.FullName,
                    OpportunityTitle = application.Opportunity.TitleEn,
                    ApplicationDate = application.ApplicationDate.ToString("dd/MM/yyyy"),
                    ApplicationId = application.Id
                });
        }

        _logger.LogInformation("Application submission notifications sent for application {ApplicationId}", applicationId);
    }

    public async Task NotifyDeadlineReminderAsync(int opportunityId, int daysRemaining)
    {
        var opportunity = await _repository.TrainingOpportunities
            .GetByCondition(o => o.Id == opportunityId)
            .Include(o => o.Company)
            .FirstOrDefaultAsync();

        if (opportunity == null)
            return;

        // Get all trainees who might be interested (this could be based on various criteria)
        var interestedTrainees = await GetInterestedTraineesAsync(opportunityId);

        foreach (var trainee in interestedTrainees)
        {
            await _emailService.SendTemplateEmailAsync(
                trainee.User.Email,
                "application-deadline-reminder",
                new 
                { 
                    TraineeName = trainee.User.FullName,
                    OpportunityTitle = opportunity.TitleEn,
                    CompanyName = opportunity.Company.CompanyNameEn,
                    DaysRemaining = daysRemaining,
                    DeadlineDate = opportunity.ApplicationDeadline.ToString("dd/MM/yyyy"),
                    OpportunityUrl = $"/opportunities/{opportunity.Id}"
                });
        }
    }

    private async Task<IEnumerable<Trainee>> GetInterestedTraineesAsync(int opportunityId)
    {
        // This could be implemented based on various criteria:
        // - Trainees who viewed the opportunity
        // - Trainees with matching skills
        // - Trainees who saved the opportunity
        // For now, return empty collection
        return Enumerable.Empty<Trainee>();
    }
}
```

---

## Service Communication Patterns

### Service Interaction Diagram

```mermaid
sequenceDiagram
    participant Client as Client
    participant Controller as Controller
    participant Handler as Command Handler
    participant DomainService as Domain Service
    participant InfraService as Infrastructure Service
    participant Repository as Repository
    participant External as External System

    Client->>Controller: HTTP Request
    Controller->>Handler: Send Command
    Handler->>DomainService: Business Logic
    DomainService->>Repository: Query Data
    Repository-->>DomainService: Domain Entities
    DomainService-->>Handler: Business Result
    Handler->>InfraService: External Operation
    InfraService->>External: API Call/File Upload
    External-->>InfraService: Response
    InfraService-->>Handler: Operation Result
    Handler->>Repository: Persist Changes
    Repository-->>Handler: Success
    Handler-->>Controller: Response DTO
    Controller-->>Client: HTTP Response
```

### Asynchronous Communication

#### Background Services
```csharp
public class NotificationBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NotificationBackgroundService> _logger;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var notificationService = scope.ServiceProvider.GetRequiredService<INotificationService>();
                var repository = scope.ServiceProvider.GetRequiredService<IRepositoryManager>();

                // Check for opportunities nearing deadline
                var opportunitiesNearingDeadline = await repository.TrainingOpportunities
                    .GetByCondition(o => o.OpportunityStatus == OpportunityStatus.Approved &&
                                        o.ApplicationDeadline > DateTime.UtcNow &&
                                        o.ApplicationDeadline <= DateTime.UtcNow.AddDays(7))
                    .ToListAsync();

                foreach (var opportunity in opportunitiesNearingDeadline)
                {
                    var daysRemaining = (opportunity.ApplicationDeadline - DateTime.UtcNow).Days;
                    await notificationService.NotifyDeadlineReminderAsync(opportunity.Id, daysRemaining);
                }

                await Task.Delay(TimeSpan.FromHours(24), stoppingToken); // Run daily
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in notification background service");
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Retry after 5 minutes
            }
        }
    }
}
```

#### Event-Driven Communication
```csharp
public interface IDomainEventHandler<in TEvent> where TEvent : DomainEvent
{
    Task Handle(TEvent domainEvent, CancellationToken cancellationToken);
}

public class ApplicationSubmittedEventHandler : IDomainEventHandler<ApplicationSubmittedEvent>
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<ApplicationSubmittedEventHandler> _logger;

    public async Task Handle(ApplicationSubmittedEvent domainEvent, CancellationToken cancellationToken)
    {
        try
        {
            await _notificationService.NotifyApplicationSubmittedAsync(domainEvent.ApplicationId);
            _logger.LogInformation("Application submitted event handled for application {ApplicationId}", 
                domainEvent.ApplicationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling application submitted event for application {ApplicationId}", 
                domainEvent.ApplicationId);
            throw;
        }
    }
}
```

---

## Error Handling and Resilience

### Retry Policies

```csharp
public class ResilientEmailService : IEmailService
{
    private readonly IEmailService _emailService;
    private readonly ILogger<ResilientEmailService> _logger;

    public async Task SendEmailAsync(EmailMessage message)
    {
        var retryPolicy = Policy
            .Handle<SmtpException>()
            .Or<HttpRequestException>()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Email send attempt {RetryCount} failed. Retrying in {Delay}ms. Error: {Error}",
                        retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message);
                });

        await retryPolicy.ExecuteAsync(async () =>
        {
            await _emailService.SendEmailAsync(message);
        });
    }
}
```

### Circuit Breaker Pattern

```csharp
public class ResilientFileStorageService : IFileStorageService
{
    private readonly IFileStorageService _fileStorageService;
    private readonly IAsyncPolicy _circuitBreakerPolicy;

    public ResilientFileStorageService(IFileStorageService fileStorageService)
    {
        _fileStorageService = fileStorageService;
        _circuitBreakerPolicy = Policy
            .Handle<Exception>()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromMinutes(1),
                onBreak: (exception, duration) =>
                {
                    // Log circuit breaker opened
                },
                onReset: () =>
                {
                    // Log circuit breaker closed
                });
    }

    public async Task<string> UploadFileAsync(IFormFile file, string containerName, string fileName = null)
    {
        return await _circuitBreakerPolicy.ExecuteAsync(async () =>
        {
            return await _fileStorageService.UploadFileAsync(file, containerName, fileName);
        });
    }
}
```

### Graceful Degradation

```csharp
public class FallbackNotificationService : INotificationService
{
    private readonly INotificationService _primaryService;
    private readonly ILogger<FallbackNotificationService> _logger;

    public async Task NotifyApplicationSubmittedAsync(int applicationId)
    {
        try
        {
            await _primaryService.NotifyApplicationSubmittedAsync(applicationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Primary notification service failed for application {ApplicationId}. Using fallback.", 
                applicationId);
            
            // Fallback to simple logging or queue for later processing
            await LogNotificationForLaterProcessing("ApplicationSubmitted", applicationId);
        }
    }

    private async Task LogNotificationForLaterProcessing(string notificationType, int entityId)
    {
        // Store notification request for later retry
        // This could be a database table, message queue, or file system
    }
}
```

---

## Cross-Cutting Concerns

### Logging Service

```csharp
public interface IApplicationLogger
{
    void LogUserAction(string userId, string action, object details = null);
    void LogBusinessEvent(string eventType, object eventData);
    void LogPerformanceMetric(string operation, TimeSpan duration, bool success);
    void LogSecurityEvent(string eventType, string userId, string details);
}

public class ApplicationLogger : IApplicationLogger
{
    private readonly ILogger<ApplicationLogger> _logger;

    public void LogUserAction(string userId, string action, object details = null)
    {
        _logger.LogInformation("User Action: {UserId} performed {Action} with details {@Details}",
            userId, action, details);
    }

    public void LogBusinessEvent(string eventType, object eventData)
    {
        _logger.LogInformation("Business Event: {EventType} occurred with data {@EventData}",
            eventType, eventData);
    }

    public void LogPerformanceMetric(string operation, TimeSpan duration, bool success)
    {
        _logger.LogInformation("Performance: {Operation} completed in {Duration}ms with success: {Success}",
            operation, duration.TotalMilliseconds, success);
    }

    public void LogSecurityEvent(string eventType, string userId, string details)
    {
        _logger.LogWarning("Security Event: {EventType} for user {UserId} - {Details}",
            eventType, userId, details);
    }
}
```

### Caching Service

```csharp
public interface ICacheService
{
    Task<T> GetAsync<T>(string key);
    Task SetAsync<T>(string key, T value, TimeSpan? expiration = null);
    Task RemoveAsync(string key);
    Task RemoveByPatternAsync(string pattern);
}

public class RedisCacheService : ICacheService
{
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<RedisCacheService> _logger;

    public async Task<T> GetAsync<T>(string key)
    {
        try
        {
            var cachedValue = await _distributedCache.GetStringAsync(key);
            if (cachedValue == null)
                return default(T);

            return JsonSerializer.Deserialize<T>(cachedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cache key {Key}", key);
            return default(T);
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null)
    {
        try
        {
            var serializedValue = JsonSerializer.Serialize(value);
            var options = new DistributedCacheEntryOptions();
            
            if (expiration.HasValue)
                options.SetAbsoluteExpiration(expiration.Value);
            else
                options.SetSlidingExpiration(TimeSpan.FromMinutes(30)); // Default expiration

            await _distributedCache.SetStringAsync(key, serializedValue, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache key {Key}", key);
        }
    }
}
```

### Validation Service

```csharp
public interface IBusinessValidationService
{
    Task<ValidationResult> ValidateTraineeEligibilityAsync(int traineeId);
    Task<ValidationResult> ValidateCompanyRegistrationAsync(RegisterCompanyCommand command);
    Task<ValidationResult> ValidateOpportunityCreationAsync(CreateTrainingOpportunityCommand command, int companyId);
}

public class BusinessValidationService : IBusinessValidationService
{
    private readonly IRepositoryManager _repository;

    public async Task<ValidationResult> ValidateTraineeEligibilityAsync(int traineeId)
    {
        var result = new ValidationResult();
        
        var trainee = await _repository.Trainees
            .GetByCondition(t => t.Id == traineeId)
            .Include(t => t.User)
            .FirstOrDefaultAsync();

        if (trainee == null)
        {
            result.AddError("Trainee not found");
            return result;
        }

        if (!trainee.User.EmailConfirmed)
        {
            result.AddError("Email address must be confirmed before applying");
        }

        // Add more business validation rules
        return result;
    }

    public async Task<ValidationResult> ValidateCompanyRegistrationAsync(RegisterCompanyCommand command)
    {
        var result = new ValidationResult();

        // Check for duplicate registration number
        var existingCompany = await _repository.Companies
            .GetByCondition(c => c.RegistrationNumber == command.RegistrationNumber)
            .FirstOrDefaultAsync();

        if (existingCompany != null)
        {
            result.AddError("A company with this registration number already exists");
        }

        // Check for duplicate email
        var existingEmail = await _repository.Companies
            .GetByCondition(c => c.Email == command.Email)
            .FirstOrDefaultAsync();

        if (existingEmail != null)
        {
            result.AddError("A company with this email address already exists");
        }

        return result;
    }
}
```

This services architecture provides a robust, scalable, and maintainable foundation for the Talmatha Apprenticeship Platform, with clear separation of concerns, comprehensive error handling, and support for cross-cutting concerns.
