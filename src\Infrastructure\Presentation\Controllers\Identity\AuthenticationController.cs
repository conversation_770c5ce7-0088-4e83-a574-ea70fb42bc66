﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Application.Features.Identity.Authentications.Commands.SignIn;
using Application.Features.Identity.Authentications.Commands.RefreshToken;
using Application.Features.Identity.Authentications.Queries.ValidateAccessToken;
using Presentation.Bases;
using Abstraction.Base.Response;
using Microsoft.AspNetCore.Http;
using Domain.Helpers;


namespace Identity.Controllers
{
    [Route("api/Users/<USER>")]
    [ApiController]
    [AllowAnonymous]
    public class AuthenticationController : AppControllerBase
    {

        [HttpPost("Sign-In")]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SignIn([FromBody] SignInCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }
       

        [HttpPost("Refresh-Token")]
        [ProducesResponseType(typeof(BaseResponse<JwtAuthResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenCommand command)
        {
            var response = await Mediator.Send(command);
            return NewResult(response);
        }

        [HttpGet("Is-Valid_Token")]
        [ProducesResponseType(typeof(BaseResponse<string>), StatusCodes.Status200OK)]
        public async Task<IActionResult> IsValidToken([FromBody] AccessTokenQuery query)
        {
            var response = await Mediator.Send(query);
            return NewResult(response);
        }

    }
}
