# Low-Level Architecture Document
## Talmatha Apprenticeship Platform

### Table of Contents
1. [Component Interactions](#component-interactions)
2. [API Specifications](#api-specifications)
3. [Data Flow Diagrams](#data-flow-diagrams)
4. [Database Design](#database-design)
5. [Implementation Patterns](#implementation-patterns)
6. [Error Handling Patterns](#error-handling-patterns)

---

## Component Interactions

### Layer Communication Flow

```mermaid
sequenceDiagram
    participant Client as Client Application
    participant Controller as API Controller
    participant Mediator as MediatR
    participant Handler as Command/Query Handler
    participant Service as Domain Service
    participant Repository as Repository
    participant Database as SQL Server

    Client->>Controller: HTTP Request
    Controller->>Mediator: Send Command/Query
    Mediator->>Handler: Route to Handler
    Handler->>Service: Business Logic
    Service->>Repository: Data Access
    Repository->>Database: SQL Query
    Database-->>Repository: Result Set
    Repository-->>Service: Domain Entities
    Service-->>Handler: Business Result
    Handler-->>Mediator: Response DTO
    Mediator-->>Controller: Mapped Response
    Controller-->>Client: HTTP Response
```

### Component Relationship Diagram

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Controllers] --> B[Middleware]
        A --> C[Filters]
    end
    
    subgraph "Application Layer"
        D[Commands] --> E[Command Handlers]
        F[Queries] --> G[Query Handlers]
        H[DTOs] --> I[Validators]
        J[Mappers] --> K[Profiles]
    end
    
    subgraph "Domain Layer"
        L[Entities] --> M[Value Objects]
        N[Domain Services] --> O[Specifications]
        P[Events] --> Q[Event Handlers]
    end
    
    subgraph "Infrastructure Layer"
        R[Repositories] --> S[DbContext]
        T[External Services] --> U[Configurations]
        V[File Storage] --> W[Blob Service]
    end
    
    A --> D
    A --> F
    E --> N
    G --> R
    E --> R
    R --> L
    S --> L
```

---

## API Specifications

### RESTful API Design Principles

#### Resource Naming Conventions
- **Collections**: Plural nouns (`/api/trainees`, `/api/companies`)
- **Resources**: Singular identifiers (`/api/trainees/{id}`)
- **Nested Resources**: Hierarchical structure (`/api/companies/{id}/opportunities`)
- **Actions**: Verb-based endpoints (`/api/trainees/{id}/applications/submit`)

#### HTTP Methods and Status Codes
- **GET**: Retrieve resources (200 OK, 404 Not Found)
- **POST**: Create resources (201 Created, 400 Bad Request)
- **PUT**: Update entire resources (200 OK, 404 Not Found)
- **PATCH**: Partial updates (200 OK, 404 Not Found)
- **DELETE**: Remove resources (204 No Content, 404 Not Found)

### API Contract Examples

#### Trainee Registration Endpoint
```json
POST /api/trainees/register
Content-Type: application/json

{
  "fullName": "Ahmed Mohamed",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "phoneNumber": "0501234567",
  "dateOfBirth": "1995-03-15",
  "termsAccepted": true
}

Response (201 Created):
{
  "success": true,
  "data": {
    "traineeId": 12345,
    "message": "Registration successful"
  },
  "errors": null
}
```

#### Training Opportunity Query Endpoint
```json
GET /api/opportunities?search=software&page=1&pageSize=10&orderBy=startDate

Response (200 OK):
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 5001,
        "title": "Software Development Apprenticeship",
        "company": "ABC Solutions",
        "location": "Riyadh, Saudi Arabia",
        "startDate": "2025-09-01",
        "applicationDeadline": "2025-08-15",
        "numberOfTrainees": 5
      }
    ],
    "totalCount": 25,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 3
  },
  "errors": null
}
```

### API Versioning Strategy
- **URL Versioning**: `/api/v1/trainees`, `/api/v2/trainees`
- **Header Versioning**: `Accept: application/vnd.talmatha.v1+json`
- **Backward Compatibility**: Maintain previous versions for defined periods

---

## Data Flow Diagrams

### Trainee Registration Flow

```mermaid
flowchart TD
    A[User Submits Registration] --> B{Validation}
    B -->|Valid| C[Create User Account]
    B -->|Invalid| D[Return Validation Errors]
    C --> E[Send Verification Email]
    E --> F[User Verifies Email]
    F --> G[Account Activated]
    G --> H[Login Redirect]
    
    D --> I[Display Error Messages]
    I --> A
```

### Training Opportunity Publication Flow

```mermaid
flowchart TD
    A[Company Rep Creates Opportunity] --> B{Form Validation}
    B -->|Valid| C[Save as Draft]
    B -->|Invalid| D[Return Validation Errors]
    C --> E[Submit for Approval]
    E --> F[Notify Institution Supervisor]
    F --> G{Supervisor Review}
    G -->|Approved| H[Publish Opportunity]
    G -->|Rejected| I[Notify Company with Reason]
    H --> J[Notify Trainees]
    I --> K[Company Can Revise]
    K --> E
    
    D --> L[Display Error Messages]
    L --> A
```

### Application Submission Flow

```mermaid
flowchart TD
    A[Trainee Views Opportunity] --> B{Already Applied?}
    B -->|Yes| C[Show Application Status]
    B -->|No| D{Deadline Passed?}
    D -->|Yes| E[Show Deadline Message]
    D -->|No| F[Show Application Form]
    F --> G[Trainee Submits Application]
    G --> H{Validation}
    H -->|Valid| I[Create Application Record]
    H -->|Invalid| J[Return Validation Errors]
    I --> K[Notify Company Representative]
    K --> L[Update Application Status]
    
    J --> M[Display Error Messages]
    M --> F
```

---

## Database Design

### Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ Trainee : extends
    User ||--o{ CompanyRepresentative : extends
    User ||--o{ InstitutionSupervisor : extends
    
    CompanyRepresentative ||--|| Company : represents
    Company ||--o{ TrainingOpportunity : publishes
    TrainingOpportunity ||--o{ Application : receives
    Trainee ||--o{ Application : submits
    
    Company {
        int Id PK
        string CompanyNameEn
        string CompanyNameAr
        string Email UK
        string RegistrationNumber UK
        string IndustrySector
        string Description
        string ContactPersonName
        string ContactPersonPhone
        string LogoUrl
        string CommercialRegDoc
        string BusinessLicenseDoc
        enum ApprovalStatus
        string RejectionReason
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }
    
    TrainingOpportunity {
        int Id PK
        int CompanyId FK
        string TitleEn
        string TitleAr
        string DescriptionEn
        string DescriptionAr
        date StartDate
        date EndDate
        string Location
        string RequiredSkills
        string Benefits
        int NumberOfTrainees
        date ApplicationDeadline
        enum OpportunityStatus
        string RejectionReason
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }
    
    Application {
        int Id PK
        int TraineeId FK
        int OpportunityId FK
        date ApplicationDate
        enum Status
        string CoverLetterEn
        string CoverLetterAr
        string CVUrl
        string TranscriptsUrl
        string CompanyFeedback
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }
    
    Trainee {
        int Id PK
        int UserId FK
        date DateOfBirth
        string Qualifications
        string Skills
        string Experience
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }
```

### Database Constraints and Indexes

#### Primary Keys and Foreign Keys
- All entities have auto-generated integer primary keys
- Foreign key relationships enforce referential integrity
- Cascade delete rules prevent orphaned records

#### Unique Constraints
- User email addresses must be unique
- Company email addresses must be unique
- Company registration numbers must be unique
- One application per trainee per opportunity

#### Indexes for Performance
```sql
-- Frequently queried fields
CREATE INDEX IX_TrainingOpportunity_StartDate ON TrainingOpportunity(StartDate);
CREATE INDEX IX_TrainingOpportunity_ApplicationDeadline ON TrainingOpportunity(ApplicationDeadline);
CREATE INDEX IX_TrainingOpportunity_CompanyId ON TrainingOpportunity(CompanyId);
CREATE INDEX IX_Application_TraineeId ON Application(TraineeId);
CREATE INDEX IX_Application_OpportunityId ON Application(OpportunityId);
CREATE INDEX IX_Application_Status ON Application(Status);

-- Composite indexes for common queries
CREATE INDEX IX_TrainingOpportunity_Status_StartDate ON TrainingOpportunity(OpportunityStatus, StartDate);
CREATE INDEX IX_Application_Trainee_Status ON Application(TraineeId, Status);
```

### Complete Database Schema Diagram

```mermaid
erDiagram
    %% User Management
    User {
        int Id PK
        string UserName UK
        string Email UK
        string PasswordHash
        string FullName
        string PhoneNumber
        string Address
        string Country
        bool EmailConfirmed
        datetime CreatedAt
        datetime UpdatedAt
    }

    Role {
        int Id PK
        string Name UK
        string Description
    }

    UserRole {
        int UserId PK,FK
        int RoleId PK,FK
    }

    %% Core Business Entities
    Trainee {
        int Id PK
        int UserId FK,UK
        date DateOfBirth
        string Qualifications
        string Skills
        string Experience
        string CVUrl
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }

    Company {
        int Id PK
        string CompanyNameEn
        string CompanyNameAr
        string Email UK
        string RegistrationNumber UK
        string IndustrySector
        string Description
        string ContactPersonName
        string ContactPersonPhone
        string LogoUrl
        string CommercialRegDocUrl
        string BusinessLicenseDocUrl
        enum ApprovalStatus
        string RejectionReason
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }

    CompanyRepresentative {
        int Id PK
        int UserId FK
        int CompanyId FK
        string Position
        bool IsPrimaryContact
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }

    TrainingOpportunity {
        int Id PK
        int CompanyId FK
        string TitleEn
        string TitleAr
        string DescriptionEn
        string DescriptionAr
        date StartDate
        date EndDate
        string Location
        string RequiredSkills
        string Benefits
        int NumberOfTrainees
        date ApplicationDeadline
        enum OpportunityStatus
        string RejectionReason
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }

    Application {
        int Id PK
        int TraineeId FK
        int OpportunityId FK
        date ApplicationDate
        enum Status
        string CoverLetterEn
        string CoverLetterAr
        string CVUrl
        string TranscriptsUrl
        string CompanyFeedback
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
        datetime DeletedAt
        int DeletedBy
        bool IsDeleted
    }

    %% Supporting Entities
    IndustrySector {
        int Id PK
        string NameEn
        string NameAr
        string Description
        bool IsActive
    }

    Skill {
        int Id PK
        string NameEn
        string NameAr
        string Category
        bool IsActive
    }

    OpportunitySkill {
        int OpportunityId PK,FK
        int SkillId PK,FK
        bool IsRequired
    }

    Document {
        int Id PK
        string FileName
        string FileUrl
        string ContentType
        long FileSize
        string EntityType
        int EntityId
        datetime UploadedAt
        int UploadedBy
    }

    %% Audit and Approval
    CompanyApprovalAudit {
        int Id PK
        int CompanyId FK
        int ApproverId FK
        enum Action
        string Comments
        datetime ActionDate
    }

    OpportunityApprovalAudit {
        int Id PK
        int OpportunityId FK
        int ApproverId FK
        enum Action
        string Comments
        datetime ActionDate
    }

    %% Content Management
    HomepageContent {
        int Id PK
        string TitleEn
        string TitleAr
        string DescriptionEn
        string DescriptionAr
        string SearchPlaceholderEn
        string SearchPlaceholderAr
        bool IsActive
        datetime CreatedAt
        int CreatedBy
        datetime UpdatedAt
        int UpdatedBy
    }

    FeaturedOpportunity {
        int Id PK
        int OpportunityId FK
        int DisplayOrder
        date StartDate
        date EndDate
        bool IsActive
    }

    %% Relationships
    User ||--o{ UserRole : has
    Role ||--o{ UserRole : assigned_to
    User ||--o| Trainee : extends
    User ||--o{ CompanyRepresentative : represents

    Company ||--o{ CompanyRepresentative : employs
    Company ||--o{ TrainingOpportunity : publishes
    Company ||--o{ CompanyApprovalAudit : audited

    TrainingOpportunity ||--o{ Application : receives
    TrainingOpportunity ||--o{ OpportunitySkill : requires
    TrainingOpportunity ||--o{ OpportunityApprovalAudit : audited
    TrainingOpportunity ||--o| FeaturedOpportunity : featured_as

    Trainee ||--o{ Application : submits

    Skill ||--o{ OpportunitySkill : used_in

    User ||--o{ CompanyApprovalAudit : approves
    User ||--o{ OpportunityApprovalAudit : approves
```

---

## Implementation Patterns

### CQRS Implementation

#### Command Pattern
```csharp
// Command Definition
public record CreateTrainingOpportunityCommand : ICommand<BaseResponse<int>>
{
    public string TitleEn { get; init; }
    public string TitleAr { get; init; }
    public string DescriptionEn { get; init; }
    public string DescriptionAr { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public string Location { get; init; }
    public int NumberOfTrainees { get; init; }
    public DateTime ApplicationDeadline { get; init; }
}

// Command Handler
public class CreateTrainingOpportunityCommandHandler 
    : ICommandHandler<CreateTrainingOpportunityCommand, BaseResponse<int>>
{
    private readonly IRepositoryManager _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreateTrainingOpportunityCommandHandler> _logger;

    public async Task<BaseResponse<int>> Handle(
        CreateTrainingOpportunityCommand request, 
        CancellationToken cancellationToken)
    {
        // Validation
        var validator = new CreateTrainingOpportunityValidator();
        var validationResult = await validator.ValidateAsync(request, cancellationToken);
        
        if (!validationResult.IsValid)
            return BadRequest<int>(validationResult.Errors);

        // Business Logic
        var opportunity = _mapper.Map<TrainingOpportunity>(request);
        opportunity.OpportunityStatus = OpportunityStatus.PendingApproval;
        
        // Persistence
        var result = await _repository.TrainingOpportunities.AddAsync(opportunity);
        await _repository.SaveChangesAsync();
        
        // Notification
        await _notificationService.NotifyInstitutionSupervisors(opportunity);
        
        return Success(result.Id);
    }
}
```

#### Query Pattern
```csharp
// Query Definition
public record GetTrainingOpportunitiesQuery : IQuery<BaseResponse<PaginatedResult<TrainingOpportunityDto>>>
{
    public string? Search { get; init; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public string? OrderBy { get; init; }
}

// Query Handler
public class GetTrainingOpportunitiesQueryHandler 
    : IQueryHandler<GetTrainingOpportunitiesQuery, BaseResponse<PaginatedResult<TrainingOpportunityDto>>>
{
    public async Task<BaseResponse<PaginatedResult<TrainingOpportunityDto>>> Handle(
        GetTrainingOpportunitiesQuery request, 
        CancellationToken cancellationToken)
    {
        var query = _repository.TrainingOpportunities
            .GetByCondition(o => o.OpportunityStatus == OpportunityStatus.Approved)
            .Include(o => o.Company);

        if (!string.IsNullOrWhiteSpace(request.Search))
        {
            query = query.Where(o => 
                o.TitleEn.Contains(request.Search) || 
                o.TitleAr.Contains(request.Search));
        }

        var result = await _mapper.ProjectTo<TrainingOpportunityDto>(query)
            .ToPaginatedListAsync(request.PageNumber, request.PageSize, request.OrderBy);

        return Success(result);
    }
}
```

### Repository Pattern Implementation

```csharp
// Generic Repository Interface
public interface IGenericRepository<T> where T : BaseEntity
{
    Task<T> GetByIdAsync(int id);
    IQueryable<T> GetByCondition(Expression<Func<T, bool>> expression, bool trackChanges = false);
    Task<T> AddAsync(T entity);
    void Update(T entity);
    void Delete(T entity);
}

// Specific Repository Interface
public interface ITrainingOpportunityRepository : IGenericRepository<TrainingOpportunity>
{
    Task<IEnumerable<TrainingOpportunity>> GetActiveOpportunitiesAsync();
    Task<IEnumerable<TrainingOpportunity>> GetOpportunitiesByCompanyAsync(int companyId);
    Task<bool> HasActiveApplicationAsync(int traineeId, int opportunityId);
}

// Repository Implementation
public class TrainingOpportunityRepository : GenericRepository<TrainingOpportunity>, ITrainingOpportunityRepository
{
    public async Task<IEnumerable<TrainingOpportunity>> GetActiveOpportunitiesAsync()
    {
        return await GetByCondition(o => 
            o.OpportunityStatus == OpportunityStatus.Approved &&
            o.ApplicationDeadline > DateTime.UtcNow, false)
            .Include(o => o.Company)
            .ToListAsync();
    }
}
```

---

## Error Handling Patterns

### Global Exception Handling

```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unhandled exception occurred");
            await HandleExceptionAsync(context, ex);
        }
    }

    private static async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = exception switch
        {
            ValidationException => new BaseResponse<object>
            {
                Success = false,
                Errors = new[] { exception.Message },
                StatusCode = HttpStatusCode.BadRequest
            },
            NotFoundException => new BaseResponse<object>
            {
                Success = false,
                Errors = new[] { exception.Message },
                StatusCode = HttpStatusCode.NotFound
            },
            UnauthorizedException => new BaseResponse<object>
            {
                Success = false,
                Errors = new[] { "Unauthorized access" },
                StatusCode = HttpStatusCode.Unauthorized
            },
            _ => new BaseResponse<object>
            {
                Success = false,
                Errors = new[] { "An internal server error occurred" },
                StatusCode = HttpStatusCode.InternalServerError
            }
        };

        context.Response.StatusCode = (int)response.StatusCode;
        context.Response.ContentType = "application/json";
        
        var jsonResponse = JsonSerializer.Serialize(response);
        await context.Response.WriteAsync(jsonResponse);
    }
}
```

### Validation Error Handling

```csharp
public class ValidationBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);
            var validationResults = await Task.WhenAll(_validators.Select(v => v.ValidateAsync(context, cancellationToken)));
            var failures = validationResults.SelectMany(r => r.Errors).Where(f => f != null).ToList();

            if (failures.Count != 0)
            {
                var errorMessages = failures.Select(f => f.ErrorMessage).ToArray();
                
                if (typeof(TResponse).IsGenericType && typeof(TResponse).GetGenericTypeDefinition() == typeof(BaseResponse<>))
                {
                    var responseType = typeof(TResponse).GetGenericArguments()[0];
                    var badRequestMethod = typeof(BaseResponseHandler).GetMethod("BadRequest", new[] { typeof(string[]) });
                    var genericBadRequest = badRequestMethod.MakeGenericMethod(responseType);
                    return (TResponse)genericBadRequest.Invoke(null, new object[] { errorMessages });
                }
                
                throw new ValidationException(string.Join("; ", errorMessages));
            }
        }

        return await next();
    }
}
```
