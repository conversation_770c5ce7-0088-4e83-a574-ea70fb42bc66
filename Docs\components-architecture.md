# Components Architecture Document
## Talmatha Apprenticeship Platform

### Table of Contents
1. [Domain Layer Components](#domain-layer-components)
2. [Application Layer Components](#application-layer-components)
3. [Infrastructure Layer Components](#infrastructure-layer-components)
4. [Presentation Layer Components](#presentation-layer-components)
5. [Component Lifecycle](#component-lifecycle)
6. [Component Dependencies](#component-dependencies)

---

## Domain Layer Components

### Core Entities

#### User Hierarchy
```csharp
// Base User Entity (ASP.NET Core Identity)
public class User : IdentityUser<int>
{
    public string FullName { get; set; }
    public string? Address { get; set; }
    public string? Country { get; set; }
    public virtual ICollection<UserRefreshToken> UserRefreshTokens { get; set; }
}

// Trainee Entity
public class Trainee : FullAuditedEntity
{
    public int UserId { get; set; }
    public User User { get; set; }
    public DateTime? DateOfBirth { get; set; }
    public string? Qualifications { get; set; }
    public string? Skills { get; set; }
    public string? Experience { get; set; }
    public virtual ICollection<Application> Applications { get; set; }
}

// Company Representative Entity
public class CompanyRepresentative : FullAuditedEntity
{
    public int UserId { get; set; }
    public User User { get; set; }
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    public string Position { get; set; }
    public bool IsPrimaryContact { get; set; }
}
```

#### Business Entities
```csharp
// Company Entity
public class Company : FullAuditedEntity
{
    public string CompanyNameEn { get; set; }
    public string CompanyNameAr { get; set; }
    public string Email { get; set; }
    public string RegistrationNumber { get; set; }
    public string IndustrySector { get; set; }
    public string? Description { get; set; }
    public string ContactPersonName { get; set; }
    public string ContactPersonPhone { get; set; }
    public string? LogoUrl { get; set; }
    public string CommercialRegDocumentUrl { get; set; }
    public string BusinessLicenseDocumentUrl { get; set; }
    public ApprovalStatus ApprovalStatus { get; set; }
    public string? RejectionReason { get; set; }
    
    // Navigation Properties
    public virtual ICollection<CompanyRepresentative> Representatives { get; set; }
    public virtual ICollection<TrainingOpportunity> TrainingOpportunities { get; set; }
}

// Training Opportunity Entity
public class TrainingOpportunity : FullAuditedEntity
{
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    public string TitleEn { get; set; }
    public string TitleAr { get; set; }
    public string DescriptionEn { get; set; }
    public string DescriptionAr { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Location { get; set; }
    public string? RequiredSkills { get; set; }
    public string? Benefits { get; set; }
    public int NumberOfTrainees { get; set; }
    public DateTime ApplicationDeadline { get; set; }
    public OpportunityStatus OpportunityStatus { get; set; }
    public string? RejectionReason { get; set; }
    
    // Navigation Properties
    public virtual ICollection<Application> Applications { get; set; }
    public virtual ICollection<OpportunityAttachment> Attachments { get; set; }
}

// Application Entity
public class Application : FullAuditedEntity
{
    public int TraineeId { get; set; }
    public Trainee Trainee { get; set; }
    public int OpportunityId { get; set; }
    public TrainingOpportunity Opportunity { get; set; }
    public DateTime ApplicationDate { get; set; }
    public ApplicationStatus Status { get; set; }
    public string? CoverLetterEn { get; set; }
    public string? CoverLetterAr { get; set; }
    public string CVUrl { get; set; }
    public string? AcademicTranscriptsUrl { get; set; }
    public string? CompanyFeedback { get; set; }
    
    // Navigation Properties
    public virtual ICollection<ApplicationDocument> Documents { get; set; }
}
```

#### Value Objects
```csharp
// Contact Information Value Object
public class ContactInfo : ValueObject
{
    public string Email { get; private set; }
    public string Phone { get; private set; }
    public string? Address { get; private set; }

    public ContactInfo(string email, string phone, string? address = null)
    {
        Email = email ?? throw new ArgumentNullException(nameof(email));
        Phone = phone ?? throw new ArgumentNullException(nameof(phone));
        Address = address;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Email;
        yield return Phone;
        yield return Address ?? string.Empty;
    }
}

// Date Range Value Object
public class DateRange : ValueObject
{
    public DateTime StartDate { get; private set; }
    public DateTime EndDate { get; private set; }

    public DateRange(DateTime startDate, DateTime endDate)
    {
        if (endDate <= startDate)
            throw new ArgumentException("End date must be after start date");

        StartDate = startDate;
        EndDate = endDate;
    }

    public int DurationInDays => (EndDate - StartDate).Days;
    public bool IsActive => DateTime.UtcNow >= StartDate && DateTime.UtcNow <= EndDate;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return StartDate;
        yield return EndDate;
    }
}
```

#### Domain Services
```csharp
// Application Eligibility Service
public interface IApplicationEligibilityService
{
    Task<bool> CanApplyAsync(int traineeId, int opportunityId);
    Task<EligibilityResult> CheckEligibilityAsync(int traineeId, int opportunityId);
}

public class ApplicationEligibilityService : IApplicationEligibilityService
{
    private readonly IRepositoryManager _repository;

    public async Task<bool> CanApplyAsync(int traineeId, int opportunityId)
    {
        var opportunity = await _repository.TrainingOpportunities.GetByIdAsync(opportunityId);
        if (opportunity == null || opportunity.OpportunityStatus != OpportunityStatus.Approved)
            return false;

        if (opportunity.ApplicationDeadline < DateTime.UtcNow)
            return false;

        var existingApplication = await _repository.Applications
            .GetByCondition(a => a.TraineeId == traineeId && a.OpportunityId == opportunityId)
            .FirstOrDefaultAsync();

        return existingApplication == null;
    }
}

// Company Approval Service
public interface ICompanyApprovalService
{
    Task<ApprovalResult> ApproveCompanyAsync(int companyId, int approverId);
    Task<ApprovalResult> RejectCompanyAsync(int companyId, int approverId, string reason);
}
```

#### Domain Events
```csharp
// Base Domain Event
public abstract class DomainEvent
{
    public DateTime OccurredOn { get; protected set; }
    public Guid Id { get; protected set; }

    protected DomainEvent()
    {
        Id = Guid.NewGuid();
        OccurredOn = DateTime.UtcNow;
    }
}

// Specific Domain Events
public class TraineeRegisteredEvent : DomainEvent
{
    public int TraineeId { get; }
    public string Email { get; }
    public string FullName { get; }

    public TraineeRegisteredEvent(int traineeId, string email, string fullName)
    {
        TraineeId = traineeId;
        Email = email;
        FullName = fullName;
    }
}

public class CompanyApprovedEvent : DomainEvent
{
    public int CompanyId { get; }
    public string CompanyName { get; }
    public int ApprovedBy { get; }

    public CompanyApprovedEvent(int companyId, string companyName, int approvedBy)
    {
        CompanyId = companyId;
        CompanyName = companyName;
        ApprovedBy = approvedBy;
    }
}

public class ApplicationSubmittedEvent : DomainEvent
{
    public int ApplicationId { get; }
    public int TraineeId { get; }
    public int OpportunityId { get; }

    public ApplicationSubmittedEvent(int applicationId, int traineeId, int opportunityId)
    {
        ApplicationId = applicationId;
        TraineeId = traineeId;
        OpportunityId = opportunityId;
    }
}
```

---

## Application Layer Components

### Commands and Queries

#### Command Structure
```csharp
// Base Command Interface
public interface ICommand<out TResponse> : IRequest<TResponse> { }

// Trainee Registration Command
public record RegisterTraineeCommand : ICommand<BaseResponse<int>>
{
    public string FullName { get; init; }
    public string Email { get; init; }
    public string Password { get; init; }
    public string? PhoneNumber { get; init; }
    public DateTime? DateOfBirth { get; init; }
    public bool TermsAccepted { get; init; }
}

// Company Registration Command
public record RegisterCompanyCommand : ICommand<BaseResponse<int>>
{
    public string CompanyNameEn { get; init; }
    public string CompanyNameAr { get; init; }
    public string Email { get; init; }
    public string RegistrationNumber { get; init; }
    public string IndustrySector { get; init; }
    public string? Description { get; init; }
    public string ContactPersonName { get; init; }
    public string ContactPersonPhone { get; init; }
    public IFormFile? Logo { get; init; }
    public IFormFile CommercialRegistrationDocument { get; init; }
    public IFormFile BusinessLicenseDocument { get; init; }
}

// Submit Application Command
public record SubmitApplicationCommand : ICommand<BaseResponse<int>>
{
    public int TraineeId { get; init; }
    public int OpportunityId { get; init; }
    public string? CoverLetterEn { get; init; }
    public string? CoverLetterAr { get; init; }
    public IFormFile CV { get; init; }
    public IFormFile? AcademicTranscripts { get; init; }
}
```

#### Query Structure
```csharp
// Base Query Interface
public interface IQuery<out TResponse> : IRequest<TResponse> { }

// Get Training Opportunities Query
public record GetTrainingOpportunitiesQuery : IQuery<BaseResponse<PaginatedResult<TrainingOpportunityDto>>>
{
    public string? Search { get; init; }
    public string? Location { get; init; }
    public string? IndustrySector { get; init; }
    public DateTime? StartDateFrom { get; init; }
    public DateTime? StartDateTo { get; init; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
    public string? OrderBy { get; init; }
}

// Get Trainee Applications Query
public record GetTraineeApplicationsQuery : IQuery<BaseResponse<PaginatedResult<ApplicationDto>>>
{
    public int TraineeId { get; init; }
    public ApplicationStatus? Status { get; init; }
    public int PageNumber { get; init; } = 1;
    public int PageSize { get; init; } = 10;
}

// Get Company Profile Query
public record GetCompanyProfileQuery : IQuery<BaseResponse<CompanyProfileDto>>
{
    public int CompanyId { get; init; }
}
```

### Data Transfer Objects (DTOs)

#### Response DTOs
```csharp
// Training Opportunity DTO
public record TrainingOpportunityDto
{
    public int Id { get; init; }
    public string TitleEn { get; init; }
    public string TitleAr { get; init; }
    public string DescriptionEn { get; init; }
    public string DescriptionAr { get; init; }
    public DateTime StartDate { get; init; }
    public DateTime EndDate { get; init; }
    public string Location { get; init; }
    public string? RequiredSkills { get; init; }
    public string? Benefits { get; init; }
    public int NumberOfTrainees { get; init; }
    public DateTime ApplicationDeadline { get; init; }
    public CompanyBasicDto Company { get; init; }
    public bool CanApply { get; init; }
}

// Application DTO
public record ApplicationDto
{
    public int Id { get; init; }
    public DateTime ApplicationDate { get; init; }
    public ApplicationStatus Status { get; init; }
    public TrainingOpportunityBasicDto Opportunity { get; init; }
    public string? CompanyFeedback { get; init; }
    public bool CanWithdraw { get; init; }
}

// Company Profile DTO
public record CompanyProfileDto
{
    public int Id { get; init; }
    public string CompanyNameEn { get; init; }
    public string CompanyNameAr { get; init; }
    public string Email { get; init; }
    public string RegistrationNumber { get; init; }
    public string IndustrySector { get; init; }
    public string? Description { get; init; }
    public string ContactPersonName { get; init; }
    public string ContactPersonPhone { get; init; }
    public string? LogoUrl { get; init; }
    public ApprovalStatus ApprovalStatus { get; init; }
    public string? RejectionReason { get; init; }
    public int TotalOpportunities { get; init; }
    public int ActiveOpportunities { get; init; }
}
```

### Validators

#### FluentValidation Validators
```csharp
// Register Trainee Command Validator
public class RegisterTraineeCommandValidator : AbstractValidator<RegisterTraineeCommand>
{
    public RegisterTraineeCommandValidator()
    {
        RuleFor(x => x.FullName)
            .NotEmpty().WithMessage("Full name is required")
            .MinimumLength(5).WithMessage("Full name must be at least 5 characters")
            .MaximumLength(100).WithMessage("Full name cannot exceed 100 characters");

        RuleFor(x => x.Email)
            .NotEmpty().WithMessage("Email is required")
            .EmailAddress().WithMessage("Invalid email format")
            .MaximumLength(255).WithMessage("Email cannot exceed 255 characters");

        RuleFor(x => x.Password)
            .NotEmpty().WithMessage("Password is required")
            .MinimumLength(8).WithMessage("Password must be at least 8 characters")
            .Matches(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]")
            .WithMessage("Password must contain uppercase, lowercase, number, and special character");

        RuleFor(x => x.TermsAccepted)
            .Equal(true).WithMessage("Terms and conditions must be accepted");

        RuleFor(x => x.DateOfBirth)
            .LessThan(DateTime.Today.AddYears(-16))
            .WithMessage("Trainee must be at least 16 years old")
            .When(x => x.DateOfBirth.HasValue);
    }
}

// Submit Application Command Validator
public class SubmitApplicationCommandValidator : AbstractValidator<SubmitApplicationCommand>
{
    private readonly IRepositoryManager _repository;

    public SubmitApplicationCommandValidator(IRepositoryManager repository)
    {
        _repository = repository;

        RuleFor(x => x.TraineeId)
            .GreaterThan(0).WithMessage("Invalid trainee ID");

        RuleFor(x => x.OpportunityId)
            .GreaterThan(0).WithMessage("Invalid opportunity ID")
            .MustAsync(BeValidOpportunity).WithMessage("Training opportunity not found or not available");

        RuleFor(x => x.CV)
            .NotNull().WithMessage("CV is required")
            .Must(BeValidPdfFile).WithMessage("CV must be a PDF file")
            .Must(BeWithinSizeLimit).WithMessage("CV file size cannot exceed 2MB");

        RuleFor(x => new { x.TraineeId, x.OpportunityId })
            .MustAsync(async (x, cancellation) => await NotHaveExistingApplication(x.TraineeId, x.OpportunityId))
            .WithMessage("You have already applied for this opportunity");
    }

    private async Task<bool> BeValidOpportunity(int opportunityId, CancellationToken cancellation)
    {
        var opportunity = await _repository.TrainingOpportunities.GetByIdAsync(opportunityId);
        return opportunity != null && 
               opportunity.OpportunityStatus == OpportunityStatus.Approved &&
               opportunity.ApplicationDeadline > DateTime.UtcNow;
    }

    private async Task<bool> NotHaveExistingApplication(int traineeId, int opportunityId)
    {
        var existingApplication = await _repository.Applications
            .GetByCondition(a => a.TraineeId == traineeId && a.OpportunityId == opportunityId)
            .FirstOrDefaultAsync();
        return existingApplication == null;
    }

    private bool BeValidPdfFile(IFormFile file)
    {
        return file?.ContentType == "application/pdf" && 
               Path.GetExtension(file.FileName).ToLower() == ".pdf";
    }

    private bool BeWithinSizeLimit(IFormFile file)
    {
        return file?.Length <= 2 * 1024 * 1024; // 2MB
    }
}
```

### AutoMapper Profiles

```csharp
// Training Opportunity Mapping Profile
public class TrainingOpportunityProfile : Profile
{
    public TrainingOpportunityProfile()
    {
        // Command to Entity
        CreateMap<CreateTrainingOpportunityCommand, TrainingOpportunity>()
            .ForMember(dest => dest.OpportunityStatus, opt => opt.MapFrom(src => OpportunityStatus.PendingApproval))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        // Entity to DTO
        CreateMap<TrainingOpportunity, TrainingOpportunityDto>()
            .ForMember(dest => dest.Company, opt => opt.MapFrom(src => src.Company))
            .ForMember(dest => dest.CanApply, opt => opt.Ignore()); // Set by query handler

        CreateMap<TrainingOpportunity, TrainingOpportunityBasicDto>();
    }
}

// Application Mapping Profile
public class ApplicationProfile : Profile
{
    public ApplicationProfile()
    {
        CreateMap<SubmitApplicationCommand, Application>()
            .ForMember(dest => dest.ApplicationDate, opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => ApplicationStatus.Submitted))
            .ForMember(dest => dest.CVUrl, opt => opt.Ignore()) // Set by handler after file upload
            .ForMember(dest => dest.AcademicTranscriptsUrl, opt => opt.Ignore());

        CreateMap<Application, ApplicationDto>()
            .ForMember(dest => dest.Opportunity, opt => opt.MapFrom(src => src.Opportunity))
            .ForMember(dest => dest.CanWithdraw, opt => opt.Ignore()); // Set by query handler
    }
}

// Company Mapping Profile
public class CompanyProfile : Profile
{
    public CompanyProfile()
    {
        CreateMap<RegisterCompanyCommand, Company>()
            .ForMember(dest => dest.ApprovalStatus, opt => opt.MapFrom(src => ApprovalStatus.Pending))
            .ForMember(dest => dest.LogoUrl, opt => opt.Ignore()) // Set by handler after file upload
            .ForMember(dest => dest.CommercialRegDocumentUrl, opt => opt.Ignore())
            .ForMember(dest => dest.BusinessLicenseDocumentUrl, opt => opt.Ignore());

        CreateMap<Company, CompanyProfileDto>()
            .ForMember(dest => dest.TotalOpportunities, opt => opt.Ignore()) // Set by query handler
            .ForMember(dest => dest.ActiveOpportunities, opt => opt.Ignore());

        CreateMap<Company, CompanyBasicDto>();
    }
}
```

---

## Infrastructure Layer Components

### Repository Implementations

```csharp
// Generic Repository Implementation
public class GenericRepository<T> : IGenericRepository<T> where T : BaseEntity
{
    protected readonly AppDbContext _context;
    protected readonly DbSet<T> _dbSet;

    public GenericRepository(AppDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    public async Task<T> GetByIdAsync(int id)
    {
        return await _dbSet.FindAsync(id);
    }

    public IQueryable<T> GetByCondition(Expression<Func<T, bool>> expression, bool trackChanges = false)
    {
        return trackChanges ? 
            _dbSet.Where(expression) : 
            _dbSet.Where(expression).AsNoTracking();
    }

    public async Task<T> AddAsync(T entity)
    {
        await _dbSet.AddAsync(entity);
        return entity;
    }

    public void Update(T entity)
    {
        _dbSet.Update(entity);
    }

    public void Delete(T entity)
    {
        _dbSet.Remove(entity);
    }
}

// Specific Repository Implementations
public class TrainingOpportunityRepository : GenericRepository<TrainingOpportunity>, ITrainingOpportunityRepository
{
    public TrainingOpportunityRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<TrainingOpportunity>> GetActiveOpportunitiesAsync()
    {
        return await _dbSet
            .Where(o => o.OpportunityStatus == OpportunityStatus.Approved && 
                       o.ApplicationDeadline > DateTime.UtcNow)
            .Include(o => o.Company)
            .OrderBy(o => o.ApplicationDeadline)
            .ToListAsync();
    }

    public async Task<IEnumerable<TrainingOpportunity>> GetOpportunitiesByCompanyAsync(int companyId)
    {
        return await _dbSet
            .Where(o => o.CompanyId == companyId)
            .Include(o => o.Applications)
            .OrderByDescending(o => o.CreatedAt)
            .ToListAsync();
    }

    public async Task<bool> HasActiveApplicationAsync(int traineeId, int opportunityId)
    {
        return await _context.Applications
            .AnyAsync(a => a.TraineeId == traineeId && 
                          a.OpportunityId == opportunityId);
    }
}

public class ApplicationRepository : GenericRepository<Application>, IApplicationRepository
{
    public ApplicationRepository(AppDbContext context) : base(context) { }

    public async Task<IEnumerable<Application>> GetApplicationsByTraineeAsync(int traineeId)
    {
        return await _dbSet
            .Where(a => a.TraineeId == traineeId)
            .Include(a => a.Opportunity)
                .ThenInclude(o => o.Company)
            .OrderByDescending(a => a.ApplicationDate)
            .ToListAsync();
    }

    public async Task<IEnumerable<Application>> GetApplicationsByOpportunityAsync(int opportunityId)
    {
        return await _dbSet
            .Where(a => a.OpportunityId == opportunityId)
            .Include(a => a.Trainee)
                .ThenInclude(t => t.User)
            .OrderByDescending(a => a.ApplicationDate)
            .ToListAsync();
    }

    public async Task<Application> GetApplicationWithDetailsAsync(int applicationId)
    {
        return await _dbSet
            .Include(a => a.Trainee)
                .ThenInclude(t => t.User)
            .Include(a => a.Opportunity)
                .ThenInclude(o => o.Company)
            .Include(a => a.Documents)
            .FirstOrDefaultAsync(a => a.Id == applicationId);
    }
}
```

### External Services

```csharp
// File Storage Service
public interface IFileStorageService
{
    Task<string> UploadFileAsync(IFormFile file, string containerName, string fileName = null);
    Task<bool> DeleteFileAsync(string fileUrl);
    Task<Stream> DownloadFileAsync(string fileUrl);
    string GetFileUrl(string containerName, string fileName);
}

public class AzureBlobStorageService : IFileStorageService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IConfiguration _configuration;

    public AzureBlobStorageService(BlobServiceClient blobServiceClient, IConfiguration configuration)
    {
        _blobServiceClient = blobServiceClient;
        _configuration = configuration;
    }

    public async Task<string> UploadFileAsync(IFormFile file, string containerName, string fileName = null)
    {
        fileName ??= $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}";
        
        var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob);
        
        var blobClient = containerClient.GetBlobClient(fileName);
        
        using var stream = file.OpenReadStream();
        await blobClient.UploadAsync(stream, overwrite: true);
        
        return blobClient.Uri.ToString();
    }

    public async Task<bool> DeleteFileAsync(string fileUrl)
    {
        try
        {
            var uri = new Uri(fileUrl);
            var containerName = uri.Segments[1].TrimEnd('/');
            var fileName = uri.Segments[2];
            
            var containerClient = _blobServiceClient.GetBlobContainerClient(containerName);
            var blobClient = containerClient.GetBlobClient(fileName);
            
            var response = await blobClient.DeleteIfExistsAsync();
            return response.Value;
        }
        catch
        {
            return false;
        }
    }
}

// Email Service
public interface IEmailService
{
    Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
    Task SendBulkEmailAsync(IEnumerable<string> recipients, string subject, string body, bool isHtml = true);
    Task SendTemplateEmailAsync(string to, string templateId, object templateData);
}

public class EmailService : IEmailService
{
    private readonly SmtpClient _smtpClient;
    private readonly IConfiguration _configuration;
    private readonly ILogger<EmailService> _logger;

    public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            var message = new MailMessage
            {
                From = new MailAddress(_configuration["Email:FromAddress"], _configuration["Email:FromName"]),
                Subject = subject,
                Body = body,
                IsBodyHtml = isHtml
            };
            
            message.To.Add(to);
            
            await _smtpClient.SendMailAsync(message);
            _logger.LogInformation("Email sent successfully to {Recipient}", to);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Recipient}", to);
            throw;
        }
    }
}

// Notification Service
public interface INotificationService
{
    Task NotifyTraineeRegistrationAsync(int traineeId);
    Task NotifyCompanyApprovalAsync(int companyId, bool approved, string reason = null);
    Task NotifyApplicationSubmittedAsync(int applicationId);
    Task NotifyOpportunityApprovedAsync(int opportunityId);
}

public class NotificationService : INotificationService
{
    private readonly IEmailService _emailService;
    private readonly IRepositoryManager _repository;
    private readonly ILogger<NotificationService> _logger;

    public async Task NotifyTraineeRegistrationAsync(int traineeId)
    {
        var trainee = await _repository.Trainees.GetByIdAsync(traineeId);
        if (trainee?.User != null)
        {
            await _emailService.SendTemplateEmailAsync(
                trainee.User.Email,
                "trainee-registration-welcome",
                new { FullName = trainee.User.FullName });
        }
    }

    public async Task NotifyApplicationSubmittedAsync(int applicationId)
    {
        var application = await _repository.Applications.GetApplicationWithDetailsAsync(applicationId);
        if (application != null)
        {
            // Notify trainee
            await _emailService.SendTemplateEmailAsync(
                application.Trainee.User.Email,
                "application-submitted-confirmation",
                new 
                { 
                    TraineeName = application.Trainee.User.FullName,
                    OpportunityTitle = application.Opportunity.TitleEn,
                    CompanyName = application.Opportunity.Company.CompanyNameEn
                });

            // Notify company representatives
            var representatives = await _repository.CompanyRepresentatives
                .GetByCondition(cr => cr.CompanyId == application.Opportunity.CompanyId)
                .Include(cr => cr.User)
                .ToListAsync();

            foreach (var rep in representatives)
            {
                await _emailService.SendTemplateEmailAsync(
                    rep.User.Email,
                    "new-application-received",
                    new 
                    { 
                        RepresentativeName = rep.User.FullName,
                        TraineeName = application.Trainee.User.FullName,
                        OpportunityTitle = application.Opportunity.TitleEn
                    });
            }
        }
    }
}
```

---

## Presentation Layer Components

### Controllers

```csharp
// Base Controller
[ApiController]
[Route("api/[controller]")]
public abstract class AppControllerBase : ControllerBase
{
    private ISender _mediator;
    protected ISender Mediator => _mediator ??= HttpContext.RequestServices.GetRequiredService<ISender>();

    protected IActionResult NewResult<T>(BaseResponse<T> response)
    {
        return response.StatusCode switch
        {
            HttpStatusCode.OK => Ok(response),
            HttpStatusCode.Created => Created(string.Empty, response),
            HttpStatusCode.BadRequest => BadRequest(response),
            HttpStatusCode.NotFound => NotFound(response),
            HttpStatusCode.Unauthorized => Unauthorized(response),
            HttpStatusCode.Forbidden => Forbid(),
            _ => StatusCode((int)response.StatusCode, response)
        };
    }
}

// Trainees Controller
[Route("api/[controller]")]
public class TraineesController : AppControllerBase
{
    [HttpPost("register")]
    [ProducesResponseType(typeof(BaseResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(BaseResponse<object>), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Register([FromBody] RegisterTraineeCommand command)
    {
        var response = await Mediator.Send(command);
        return NewResult(response);
    }

    [HttpGet("{id:int}/applications")]
    [Authorize(Roles = "trainee")]
    [ProducesResponseType(typeof(BaseResponse<PaginatedResult<ApplicationDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetApplications(
        [FromRoute] int id, 
        [FromQuery] GetTraineeApplicationsQuery query)
    {
        query = query with { TraineeId = id };
        var response = await Mediator.Send(query);
        return NewResult(response);
    }

    [HttpPost("{id:int}/applications")]
    [Authorize(Roles = "trainee")]
    [ProducesResponseType(typeof(BaseResponse<int>), StatusCodes.Status201Created)]
    public async Task<IActionResult> SubmitApplication(
        [FromRoute] int id,
        [FromForm] SubmitApplicationCommand command)
    {
        command = command with { TraineeId = id };
        var response = await Mediator.Send(command);
        return NewResult(response);
    }
}

// Training Opportunities Controller
[Route("api/training-opportunities")]
public class TrainingOpportunitiesController : AppControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(BaseResponse<PaginatedResult<TrainingOpportunityDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetOpportunities([FromQuery] GetTrainingOpportunitiesQuery query)
    {
        var response = await Mediator.Send(query);
        return Ok(response);
    }

    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(BaseResponse<TrainingOpportunityDetailDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(BaseResponse<object>), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetOpportunity([FromRoute] int id)
    {
        var query = new GetTrainingOpportunityQuery { Id = id };
        var response = await Mediator.Send(query);
        return NewResult(response);
    }

    [HttpPost]
    [Authorize(Roles = "company-representative")]
    [ProducesResponseType(typeof(BaseResponse<int>), StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateOpportunity([FromForm] CreateTrainingOpportunityCommand command)
    {
        var response = await Mediator.Send(command);
        return NewResult(response);
    }
}

// Companies Controller
[Route("api/[controller]")]
public class CompaniesController : AppControllerBase
{
    [HttpPost("register")]
    [ProducesResponseType(typeof(BaseResponse<int>), StatusCodes.Status201Created)]
    public async Task<IActionResult> Register([FromForm] RegisterCompanyCommand command)
    {
        var response = await Mediator.Send(command);
        return NewResult(response);
    }

    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(BaseResponse<CompanyProfileDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetProfile([FromRoute] int id)
    {
        var query = new GetCompanyProfileQuery { CompanyId = id };
        var response = await Mediator.Send(query);
        return NewResult(response);
    }

    [HttpPut("{id:int}/approval")]
    [Authorize(Roles = "institution-supervisor")]
    [ProducesResponseType(typeof(BaseResponse<bool>), StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateApprovalStatus(
        [FromRoute] int id,
        [FromBody] UpdateCompanyApprovalCommand command)
    {
        command = command with { CompanyId = id };
        var response = await Mediator.Send(command);
        return NewResult(response);
    }
}
```

### Middleware Components

```csharp
// Request Logging Middleware
public class RequestLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestLoggingMiddleware> _logger;

    public RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        _logger.LogInformation("Request started: {Method} {Path} from {RemoteIpAddress}",
            context.Request.Method,
            context.Request.Path,
            context.Connection.RemoteIpAddress);

        await _next(context);

        stopwatch.Stop();
        
        _logger.LogInformation("Request completed: {Method} {Path} responded {StatusCode} in {ElapsedMilliseconds}ms",
            context.Request.Method,
            context.Request.Path,
            context.Response.StatusCode,
            stopwatch.ElapsedMilliseconds);
    }
}

// Rate Limiting Middleware
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IMemoryCache _cache;
    private readonly IConfiguration _configuration;

    public async Task InvokeAsync(HttpContext context)
    {
        var clientId = GetClientIdentifier(context);
        var key = $"rate_limit_{clientId}";
        
        var requestCount = _cache.Get<int>(key);
        var limit = _configuration.GetValue<int>("RateLimit:RequestsPerMinute", 100);
        
        if (requestCount >= limit)
        {
            context.Response.StatusCode = 429; // Too Many Requests
            await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
            return;
        }
        
        _cache.Set(key, requestCount + 1, TimeSpan.FromMinutes(1));
        await _next(context);
    }

    private string GetClientIdentifier(HttpContext context)
    {
        // Use user ID if authenticated, otherwise use IP address
        return context.User.Identity?.IsAuthenticated == true
            ? context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value
            : context.Connection.RemoteIpAddress?.ToString();
    }
}
```

---

## Component Lifecycle

### Dependency Injection Lifetimes

```csharp
// Service Registration in Program.cs
public static class ServiceRegistration
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Singleton Services (created once per application)
        services.AddSingleton<IFileStorageService, AzureBlobStorageService>();
        services.AddSingleton<IMemoryCache, MemoryCache>();
        
        // Scoped Services (created once per request)
        services.AddScoped<IRepositoryManager, RepositoryManager>();
        services.AddScoped<INotificationService, NotificationService>();
        services.AddScoped<IApplicationEligibilityService, ApplicationEligibilityService>();
        services.AddScoped<ICompanyApprovalService, CompanyApprovalService>();
        
        // Transient Services (created each time requested)
        services.AddTransient<IEmailService, EmailService>();
        services.AddTransient<IValidator<RegisterTraineeCommand>, RegisterTraineeCommandValidator>();
        services.AddTransient<IValidator<SubmitApplicationCommand>, SubmitApplicationCommandValidator>();
        
        return services;
    }
}
```

### Component State Management

#### Entity State Tracking
- **Added**: New entities not yet persisted
- **Modified**: Existing entities with changes
- **Deleted**: Entities marked for deletion
- **Unchanged**: Entities loaded but not modified
- **Detached**: Entities not tracked by context

#### Application State Management
- **Stateless Controllers**: No state maintained between requests
- **Scoped Services**: State maintained within request scope
- **Domain Events**: State changes communicated through events
- **Audit Trails**: State changes tracked in audit entities

---

## Component Dependencies

### Dependency Graph

```mermaid
graph TD
    A[Controllers] --> B[MediatR]
    B --> C[Command/Query Handlers]
    C --> D[Domain Services]
    C --> E[Repositories]
    C --> F[External Services]
    
    D --> G[Domain Entities]
    E --> H[DbContext]
    H --> G
    
    F --> I[File Storage]
    F --> J[Email Service]
    F --> K[External APIs]
    
    C --> L[Validators]
    C --> M[Mappers]
    
    N[Middleware] --> A
    O[Filters] --> A
    
    P[Background Services] --> E
    P --> F
```

### Interface Segregation

Each component depends only on the interfaces it needs:
- **Controllers** depend on `ISender` (MediatR)
- **Handlers** depend on specific repository interfaces
- **Services** depend on minimal required interfaces
- **Repositories** depend on `DbContext` abstractions

### Circular Dependency Prevention

- **Domain Layer** has no dependencies on other layers
- **Application Layer** depends only on Domain abstractions
- **Infrastructure Layer** implements Application interfaces
- **Presentation Layer** depends on Application abstractions

This architecture ensures loose coupling, high cohesion, and maintainable code structure.
