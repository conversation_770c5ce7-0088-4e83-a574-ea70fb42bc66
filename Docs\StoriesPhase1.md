**User Story 1: View Apprenticeship Program Homepage**

**Main User Story Table**

| Field | Description | Content Guidelines |
| --- | --- | --- |
| **Name** | View Apprenticeship Program Homepage | Accessing the main landing page for the apprenticeship program. |
| **User Story** | As a Platform Visitor, I want to view the Apprenticeship Program Homepage so that I can get an overview of the program and available opportunities. |     |
| **Story Points** | 2   | Relatively straightforward, basic navigation. |
| **User Roles** | Platform Visitor, Trainee, Company Representative, Institution Supervisor, System Manager, Quality Officer | All users can access the homepage. |
| **Access Requirements** | No authentication required for initial view. |     |
| **Trigger** | User navigates to the platform's root URL or clicks on a homepage link. |     |
| **Frequency of Use** | High | First point of contact for most users. |
| **Pre-condition** | User has internet access and a web browser. |     |
| **Business Rules** | The homepage must display key program information, a search bar, and featured training opportunities. | Information displayed should be up-to-date. |
| **Post-condition** | User is successfully viewing the Apprenticeship Program Homepage with loaded content. |     |
| **Risk** | Slow loading times; outdated information displayed. | Optimization for performance; regular content updates. |
| **Assumptions** | Basic internet connectivity is available. |     |
| **UX/UI Design Link** | \[Link to Figma Mockup for Homepage\] (Placeholder) |     |

Export to Sheets

**Process Flow Table**

| Step | Action Description | Actor | Related Message Codes | Notes |
| --- | --- | --- | --- | --- |
| 1   | User opens web browser and navigates to the platform URL. | Platform Visitor |     |     |
| 2   | System loads the Apprenticeship Program Homepage. | System | MSG-HP-001 | If successful, content displayed. |
| 3   | User views general information, featured opportunities, and search options. | Platform Visitor |     |     |

Export to Sheets

**Alternative Flow Table**

| Alternative Scenario | Condition | Action | Related Message Codes | Resolution |
| --- | --- | --- | --- | --- |
| Page Load Failure | Network error or server downtime. | System displays an error message. | MSG-ERR-001 | User can refresh the page or try again later. |
| No Content Available | Database connection issue or no data to display. | System displays a "No Content" message. | MSG-WARN-001 | System administrators investigate data source. |

Export to Sheets

**Acceptance Criteria Table**

| Scenario | Given | When | Then |
| --- | --- | --- | --- |
| Homepage loads successfully | User is online | User navigates to the platform URL | The Apprenticeship Program Homepage is displayed with a search bar and featured content. |
| Key information is visible | Homepage is loaded | User scrolls down the page | Program overview, contact information, and relevant links are clearly visible. |
| Error message on network issue | User is offline or network fails | User tries to access the platform URL | An appropriate error message indicating connection issues is displayed. |

Export to Sheets

**Data Entities Table**

Entity Name: Homepage Content | Attribute (English) | Attribute (Arabic) | Mandatory/Optional | Attribute Type | Data Length | Integration Requirements | Default Value | Condition (if needed) | Rules (if needed) | Sample in Arabic | Sample in English | |---|---|---|---|---|---|---|---|---|---|---| | Page Title | عنوان الصفحة | Mandatory | Text | 100 | Database | "برنامج التلمذة" | | | "برنامج التلمذة" | "Apprenticeship Program" | | Program Description | وصف البرنامج | Mandatory | Text | 1000 | Database | | | | "برنامج يهدف إلى..." | "A program aiming to..." | | Featured Opportunities | فرص مميزة | Optional | Relation to Training Opportunity | | Database | | At least 3 if available | | N/A | N/A | | Search Bar Placeholder | نص البحث الافتراضي | Mandatory | Placeholder | 100 | UI | "ابحث عن فرص تدريب..." | | | "ابحث عن فرص تدريب" | "Search for training opportunities..." |

**Messages/Notifications Table**

| Message Code | Message (English) | Message (Arabic) | Message Type | Communication Method |
| --- | --- | --- | --- | --- |
| MSG-HP-001 | Homepage loaded successfully. | تم تحميل الصفحة الرئيسية بنجاح. | Notification | In-App (silent) |
| MSG-ERR-001 | Unable to load page. Please check your internet connection. | تعذر تحميل الصفحة. يرجى التحقق من اتصالك بالإنترنت. | Error | In-App |
| MSG-WARN-001 | No content available at this moment. Please try again later. | لا يوجد محتوى متاح حالياً. يرجى المحاولة لاحقاً. | Warning | In-App |

Export to Sheets

**Screen Elements Table**

| Element ID | Element Type | Element Name (English) | Element Name (Arabic) | Required/Optional | Validation Rules | Business Logic | Related Data Entity | User Interaction | Accessibility Notes |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| HP-001 | Header | Main Navigation Bar | شريط التنقل الرئيسي | Required |     | Global navigation for the platform. |     | Navigation | Accessible links, keyboard navigation. |
| HP-002 | Text Input | Search Bar | شريط البحث | Optional | Min 3 characters for search. | Allows users to search for training opportunities by keywords. | Training Opportunity | Type, click search icon/press Enter. | Clear placeholder, labels for screen readers. |
| HP-003 | Button | Search Button | زر البحث | Optional |     | Initiates search based on input. |     | Click | Keyboard accessible. |
| HP-004 | Section | Featured Opportunities Section | قسم الفرص المميزة | Optional |     | Displays a curated list of prominent training opportunities. | Featured Opportunities | Scroll, click on opportunity cards. | Clearly separated, heading for screen readers. |
| HP-005 | Text Display | Program Overview Text | نص نظرة عامة على البرنامج | Required |     | Provides a brief introduction to the apprenticeship program. | Program Description | Read. | Sufficient contrast, readable font size. |
| HP-006 | Footer | Footer Navigation | تذييل الصفحة | Required |     | Contains copyright, privacy policy, and contact info. |     | Navigation | Accessible links. |

Export to Sheets

**User Story 2: Trainee Registration**

**Main User Story Table**

| Field | Description | Content Guidelines |
| --- | --- | --- |
| **Name** | Trainee Registration | Process for new trainees to create an account on the platform. |
| **User Story** | As a Trainee, I want to register for the platform so that I can apply for training opportunities and manage my profile. |     |
| **Story Points** | 5   | Involves multiple steps, data entry, and validation. |
| **User Roles** | Trainee | Only unregistered users acting as trainees. |
| **Access Requirements** | No authentication required to initiate registration. |     |
| **Trigger** | User clicks on "Register" button or navigates to the registration page. |     |
| **Frequency of Use** | Medium | Occurs once per trainee. |
| **Pre-condition** | User is not already registered on the platform. |     |
| **Business Rules** | All mandatory fields must be filled. Email must be unique and valid. Password must meet complexity requirements. | User must agree to Terms & Conditions. |
| **Post-condition** | Trainee account is created, and user is logged in or redirected to login. | Verification email sent (if applicable). |
| **Risk** | Duplicate accounts; weak passwords; data validation issues. | Implement strong validation, unique email check, password policy. |
| **Assumptions** | User has a valid email address. |     |
| **UX/UI Design Link** | \[Link to Figma Mockup for Trainee Registration Form\] (Placeholder) |     |

Export to Sheets

**Process Flow Table**

| Step | Action Description | Actor | Related Message Codes | Notes |
| --- | --- | --- | --- | --- |
| 1   | User clicks "Register" button on homepage or login page. | Trainee |     |     |
| 2   | System displays the Trainee Registration Form. | System |     |     |
| 3   | User fills in all required fields (Name, Email, Password, etc.) and agrees to T&C. | Trainee |     |     |
| 4   | User clicks "Submit Registration" button. | Trainee |     |     |
| 5   | System validates input data (email format, uniqueness, password strength). | System | MSG-REG-002, MSG-REG-003, MSG-REG-004 | If validation fails, errors displayed. |
| 6   | If data is valid, system creates a new Trainee account. | System |     |     |
| 7   | System sends a verification email (optional) and logs in the user or redirects to login page. | System | MSG-REG-001 | Success message displayed. |

Export to Sheets

**Alternative Flow Table**

| Alternative Scenario | Condition | Action | Related Message Codes | Resolution |
| --- | --- | --- | --- | --- |
| Email Already Registered | User tries to register with an email already in use. | System displays an error message. | MSG-REG-002 | User can try to recover password or use a different email. |
| Invalid Email Format | User enters an email in an incorrect format. | System displays a validation error. | MSG-REG-003 | User corrects email format. |
| Weak Password | User enters a password that doesn't meet complexity requirements. | System displays a password strength error. | MSG-REG-004 | User must strengthen password. |
| Mandatory Field Empty | User attempts to submit with a required field left blank. | System highlights the empty field and displays an error. | MSG-REG-005 | User fills the mandatory field. |

Export to Sheets

**Acceptance Criteria Table**

| Scenario | Given | When | Then |
| --- | --- | --- | --- |
| Successful Registration | User provides valid and unique registration details | User submits the registration form | A new trainee account is created, and the user is redirected to the dashboard/login page. |
| Duplicate Email Rejection | User enters an email already registered | User submits the registration form | An error message "Email already exists" is displayed, and the account is not created. |
| Invalid Email Format | User enters "<test@.com>" as email | User submits the registration form | An error message "Invalid email format" is displayed. |
| Weak Password Rejection | User enters "123" as password | User submits the registration form | An error message about password complexity is displayed. |
| Mandatory Field Missing | User leaves "Full Name" blank | User submits the registration form | An error message "Full Name is required" is displayed. |

Export to Sheets

**Data Entities Table**

Entity Name: Trainee Account | Attribute (English) | Attribute (Arabic) | Mandatory/Optional | Attribute Type | Data Length | Integration Requirements | Default Value | Condition (if needed) | Rules (if needed) | Sample in Arabic | Sample in English | |---|---|---|---|---|---|---|---|---|---|---| | Trainee ID | معرف المتدرب | Mandatory | Number | | Database (Auto-generated) | | Unique | Primary Key | 12345 | 12345 | | Full Name | الاسم الكامل | Mandatory | Text | 100 | Database | | | Min 5 chars, Alpha-numeric | "أحمد محمد" | "Ahmed Mohamed" | | Email Address | عنوان البريد الإلكتروني | Mandatory | Text | 255 | Database | | Unique | Valid email format | "<<EMAIL>>" | "<<EMAIL>>" | | Password Hash | كلمة المرور المشفرة | Mandatory | Text | 255 | Database | | | Min 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 symbol | N/A | N/A | | Phone Number | رقم الهاتف | Optional | Text | 15 | Database | | | Numeric only | "**********" | "**********" | | Date of Birth | تاريخ الميلاد | Optional | Date | | Database | | | Must be a valid date | "1995-03-15" | "1995-03-15" | | Terms & Conditions Accepted | الموافقة على الشروط والأحكام | Mandatory | Boolean | | Database | False | | Must be True for registration | نعم | Yes | | Registration Date | تاريخ التسجيل | Mandatory | Date | | Database (Auto-generated) | Current Date | | Read-only | "2023-07-20" | "2023-07-20" |

**Messages/Notifications Table**

| Message Code | Message (English) | Message (Arabic) | Message Type | Communication Method |
| --- | --- | --- | --- | --- |
| MSG-REG-001 | Registration successful! Welcome to the Apprenticeship Program. | تم التسجيل بنجاح! مرحباً بك في برنامج التلمذة. | Success | In-App, Email |
| MSG-REG-002 | This email address is already registered. Please try logging in or use a different email. | عنوان البريد الإلكتروني هذا مسجل بالفعل. يرجى محاولة تسجيل الدخول أو استخدام بريد إلكتروني آخر. | Error | In-App |
| MSG-REG-003 | Invalid email address format. Please enter a valid email. | تنسيق عنوان البريد الإلكتروني غير صالح. يرجى إدخال بريد إلكتروني صالح. | Validation Error | In-App |
| MSG-REG-004 | Password must be at least 8 characters long and include uppercase, lowercase, numbers, and symbols. | يجب أن تتكون كلمة المرور من 8 أحرف على الأقل وأن تتضمن أحرفاً كبيرة وصغيرة وأرقاماً ورموزاً. | Validation Error | In-App |
| MSG-REG-005 | All mandatory fields must be filled. | يجب ملء جميع الحقول الإلزامية. | Validation Error | In-App |

Export to Sheets

**Screen Elements Table**

| Element ID | Element Type | Element Name (English) | Element Name (Arabic) | Required/Optional | Validation Rules | Business Logic | Related Data Entity | User Interaction | Accessibility Notes |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| REG-001 | Form | Trainee Registration Form | نموذج تسجيل المتدرب | Required |     | Collects trainee's personal and account information. | Trainee Account | Fill in fields, click submit. | Clear form labels, tab order. |
| REG-002 | Text Input | Full Name Field | حقل الاسم الكامل | Mandatory | Min 5 characters. | Collects the trainee's full name. | Full Name | Type text. | Placeholder, aria-required. |
| REG-003 | Text Input | Email Field | حقل البريد الإلكتروني | Mandatory | Valid email format, unique. | Collects the trainee's email address for communication and login. | Email Address | Type email. | type="email", aria-required. |
| REG-004 | Password Input | Password Field | حقل كلمة المرور | Mandatory | Min 8 chars, complexity rules. | Sets the trainee's password for login. | Password Hash | Type password. | Password toggle for visibility, strength indicator. |
| REG-005 | Password Input | Confirm Password Field | حقل تأكيد كلمة المرور | Mandatory | Must match Password Field. | Confirms the trainee's password. |     | Type password. | aria-required. |
| REG-006 | Checkbox | Terms & Conditions Checkbox | مربع اختيار الشروط والأحكام | Mandatory | Must be checked. | Ensures user agrees to platform terms. | Terms & Conditions Accepted | Click to check/uncheck. | Associated label, aria-required. |
| REG-007 | Link | Terms & Conditions Link | رابط الشروط والأحكام | Optional |     | Links to the full Terms & Conditions document. |     | Click to open new tab. | Clearly distinguishable link. |
| REG-008 | Button | Register Button | زر التسجيل | Required | All mandatory fields filled, T&C checked. | Submits the registration form to create the account. |     | Click. | Clear label, disabled until form valid. |
| REG-009 | Text Display | Error Message Display | عرض رسالة الخطأ | Optional |     | Displays validation or error messages to the user. | Messages/Notifications | Read. | aria-live for dynamic updates. |

Export to Sheets

**User Story 3: Company Registration and Profile Creation**

**Main User Story Table**

| Field | Description | Content Guidelines |
| --- | --- | --- |
| **Name** | Company Registration and Profile Creation | Process for companies to register and create their organizational profile on the platform. |
| **User Story** | As a Company Representative, I want to register and create a profile for my company so that I can publish training opportunities and manage applications. |     |
| **Story Points** | 8   | Complex process involving multiple data inputs, potential document uploads, and an approval workflow. |
| **User Roles** | Company Representative | Represents an organization wishing to join the program. |
| **Access Requirements** | No authentication required to initiate registration. |     |
| **Trigger** | User clicks on "Register Company" button or navigates to the company registration page. |     |
| **Frequency of Use** | Low | Occurs once per company. |
| **Pre-condition** | Company is not already registered on the platform. Company Representative has authority to register the company. |     |
| **Business Rules** | All mandatory fields must be filled. Company email must be unique and valid. Company registration number must be valid. Company profile requires administrative approval. |     |
| **Post-condition** | Company profile is submitted for approval, and the representative receives confirmation. |     |
| **Risk** | Fraudulent company registrations; incomplete profiles; delays in approval process. | Implement robust validation, clear approval workflow, provide status updates. |
| **Assumptions** | Company Representative has all required company information and documents for registration. |     |
| **UX/UI Design Link** | \[Link to Figma Mockup for Company Registration Form\] (Placeholder) |     |

Export to Sheets

**Process Flow Table**

| Step | Action Description | Actor | Related Message Codes | Notes |
| --- | --- | --- | --- | --- |
| 1   | User navigates to the "Register Company" page. | Company Representative |     |     |
| 2   | System displays the Company Registration Form. | System |     |     |
| 3   | User fills in mandatory company details (Name, Email, Industry, Registration Number, etc.). | Company Representative |     |     |
| 4   | User uploads required documents (e.g., commercial registration, business license). | Company Representative |     |     |
| 5   | User agrees to the platform's terms for companies. | Company Representative |     |     |
| 6   | User clicks "Submit Company Registration" button. | Company Representative |     |     |
| 7   | System validates input data and uploaded documents. | System | MSG-COMP-002, MSG-COMP-003, MSG-COMP-004 | If validation fails, errors displayed. |
| 8   | If data is valid, system creates a pending Company Profile. | System |     |     |
| 9   | System sends a confirmation email to the Company Representative. | System | MSG-COMP-001 |     |
| 10  | System notifies Institution Supervisors/System Managers for approval. | System |     |     |
| 11  | Institution Supervisor reviews the company profile and documents. | Institution Supervisor |     | Manual review. |
| 12  | Institution Supervisor approves or rejects the company profile. | Institution Supervisor | MSG-COMP-005, MSG-COMP-006 | Notification sent to Company Representative. |

Export to Sheets

**Alternative Flow Table**

| Alternative Scenario | Condition | Action | Related Message Codes | Resolution |
| --- | --- | --- | --- | --- |
| Duplicate Company Email | User registers with an email already associated with a company. | System displays an error message. | MSG-COMP-002 | User tries logging in or uses a different email. |
| Invalid Registration Number | User enters an incorrect or invalid company registration number. | System displays a validation error. | MSG-COMP-003 | User corrects the registration number. |
| Document Upload Failure | User's document upload fails due to size/format. | System displays an error message. | MSG-COMP-004 | User tries again with correct file type/size. |
| Profile Rejection by Supervisor | Institution Supervisor rejects the company profile. | System sends a rejection notification with reason. | MSG-COMP-006 | Company Representative can amend and resubmit or contact support. |
| Mandatory Field Empty | User attempts to submit with a required field left blank. | System highlights the empty field and displays an error. | MSG-COMP-007 | User fills the mandatory field. |

Export to Sheets

**Acceptance Criteria Table**

| Scenario | Given | When | Then |
| --- | --- | --- | --- |
| Successful Submission for Approval | User provides valid company details and uploads documents | User submits the registration form | A new company profile is created with status "Pending Approval", and a confirmation is sent to the representative. |
| Rejection of Duplicate Company | User attempts to register with an existing company name/registration | User submits the registration form | An error message indicating duplication is displayed, and no new profile is created. |
| Profile Approval by Supervisor | A company profile is in "Pending Approval" status | Institution Supervisor approves the profile | The company profile status changes to "Approved", and the representative is notified. |
| Profile Rejection by Supervisor | A company profile is in "Pending Approval" status | Institution Supervisor rejects the profile with a reason | The company profile status changes to "Rejected", and the representative is notified with the reason. |
| Mandatory Document Missing | User does not upload a required document | User submits the registration form | An error message indicating missing documents is displayed. |

Export to Sheets

**Data Entities Table**

Entity Name: Company Profile | Attribute (English) | Attribute (Arabic) | Mandatory/Optional | Attribute Type | Data Length | Integration Requirements | Default Value | Condition (if needed) | Rules (if needed) | Sample in Arabic | Sample in English | |---|---|---|---|---|---|---|---|---|---|---| | Company ID | معرف الشركة | Mandatory | Number | | Database (Auto-generated) | | Unique | Primary Key | 98765 | 98765 | | Company Name (English) | اسم الشركة (إنجليزية) | Mandatory | Text | 255 | Database | | Unique | | "ABC Solutions" | "ABC Solutions" | | Company Name (Arabic) | اسم الشركة (عربية) | Mandatory | Text | 255 | Database | | | | "حلول ألفا بي سي" | "ABC Solutions" | | Company Email | بريد الشركة الإلكتروني | Mandatory | Text | 255 | Database | | Unique | Valid email format | "<<EMAIL>>" | "<<EMAIL>>" | | Company Registration Number | رقم التسجيل التجاري | Mandatory | Text | 50 | Database | | Unique | Valid format for commercial registration numbers | "CRN-123456" | "CRN-123456" | | Industry Sector | قطاع الصناعة | Mandatory | Dropdown | | Database | | | Select from predefined list | "تكنولوجيا المعلومات" | "Information Technology" | | Company Description | وصف الشركة | Optional | Text | 2000 | Database | | | | "شركة متخصصة في تطوير البرمجيات." | "A software development company." | | Contact Person Name | اسم جهة الاتصال | Mandatory | Text | 100 | Database | | | | "سارة علي" | "Sara Ali" | | Contact Person Phone | هاتف جهة الاتصال | Mandatory | Text | 15 | Database | | | Numeric only | "**********" | "**********" | | Company Logo | شعار الشركة | Optional | File Upload | | File Storage | | | Max 2MB, JPG/PNG/SVG | N/A | N/A | | Commercial Registration Document | وثيقة السجل التجاري | Mandatory | File Upload | | File Storage | | | PDF only, Max 5MB | N/A | N/A | | Business License Document | وثيقة رخصة العمل | Mandatory | File Upload | | File Storage | | | PDF only, Max 5MB | N/A | N/A | | Approval Status | حالة الموافقة | Mandatory | Text (Enum) | 20 | Database | "Pending" | | Enum values: Pending, Approved, Rejected | "قيد الموافقة" | "Pending" | | Rejection Reason | سبب الرفض | Optional | Text | 500 | Database | | Only if Approval Status is "Rejected" | | "مستندات غير مكتملة" | "Incomplete documents" |

**Messages/Notifications Table**

| Message Code | Message (English) | Message (Arabic) | Message Type | Communication Method |
| --- | --- | --- | --- | --- |
| MSG-COMP-001 | Company registration submitted successfully for review. You will be notified once approved. | تم تقديم طلب تسجيل الشركة بنجاح للمراجعة. سيتم إخطارك فور الموافقة عليه. | Success | In-App, Email |
| MSG-COMP-002 | This company name or email is already registered. Please contact support if you believe this is an error. | اسم هذه الشركة أو بريدها الإلكتروني مسجل بالفعل. يرجى الاتصال بالدعم إذا كنت تعتقد أن هذا خطأ. | Error | In-App |
| MSG-COMP-003 | Invalid company registration number. Please check and re-enter. | رقم التسجيل التجاري غير صالح. يرجى التحقق وإعادة الإدخال. | Validation Error | In-App |
| MSG-COMP-004 | File upload failed. Please ensure documents are in PDF format and less than 5MB. | فشل تحميل الملف. يرجى التأكد من أن المستندات بصيغة PDF وأقل من 5 ميجابايت. | Error | In-App |
| MSG-COMP-005 | Your company profile has been approved! You can now log in and publish training opportunities. | تمت الموافقة على ملف تعريف شركتك! يمكنك الآن تسجيل الدخول ونشر فرص التدريب. | Success | Email, In-App |
| MSG-COMP-006 | Your company profile has been rejected due to: \[Rejection Reason\]. Please review and resubmit. | تم رفض ملف تعريف شركتك بسبب: \[سبب الرفض\]. يرجى المراجعة وإعادة التقديم. | Error | Email, In-App |
| MSG-COMP-007 | All mandatory fields must be filled. | يجب ملء جميع الحقول الإلزامية. | Validation Error | In-App |

Export to Sheets

**Screen Elements Table**

| Element ID | Element Type | Element Name (English) | Element Name (Arabic) | Required/Optional | Validation Rules | Business Logic | Related Data Entity | User Interaction | Accessibility Notes |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| COMP-001 | Form | Company Registration Form | نموذج تسجيل الشركة | Required |     | Collects details for company profile creation. | Company Profile | Fill in fields, upload files, click submit. | Clear form labels, logical grouping. |
| COMP-002 | Text Input | Company Name (English) | اسم الشركة (إنجليزية) | Mandatory | Unique, Min 3 chars. | Official English name of the company. | Company Name (English) | Type text. | Placeholder, aria-required. |
| COMP-003 | Text Input | Company Name (Arabic) | اسم الشركة (عربية) | Mandatory | Unique, Min 3 chars. | Official Arabic name of the company. | Company Name (Arabic) | Type text. | Placeholder, aria-required. |
| COMP-004 | Text Input | Company Email Field | حقل بريد الشركة الإلكتروني | Mandatory | Valid email format, unique. | Main contact email for the company. | Company Email | Type email. | type="email", aria-required. |
| COMP-005 | Text Input | Company Registration Number Field | حقل رقم التسجيل التجاري | Mandatory | Valid format, unique. | Official commercial registration number. | Company Registration Number | Type text. | aria-required. |
| COMP-006 | Dropdown | Industry Sector Select | اختيار قطاع الصناعة | Mandatory | Select from predefined list. | Classifies the company's primary business. | Industry Sector | Select from dropdown. | aria-required. |
| COMP-007 | Text Area | Company Description Field | حقل وصف الشركة | Optional | Max 2000 characters. | Provides a brief overview of the company. | Company Description | Type text. | Resize handle, character counter. |
| COMP-008 | File Upload | Commercial Registration Upload | تحميل السجل التجاري | Mandatory | PDF only, Max 5MB. | Allows uploading of the company's commercial registration document. | Commercial Registration Document | Click to upload file. | Clear instructions, file type/size limits. |
| COMP-009 | File Upload | Business License Upload | تحميل رخصة العمل | Mandatory | PDF only, Max 5MB. | Allows uploading of the company's business license. | Business License Document | Click to upload file. | Clear instructions, file type/size limits. |
| COMP-010 | Checkbox | Company Terms & Conditions Checkbox | مربع اختيار شروط وأحكام الشركة | Mandatory | Must be checked. | Ensures agreement to company-specific terms. |     | Click to check/uncheck. | Associated label, aria-required. |
| COMP-011 | Button | Submit Registration Button | زر تقديم التسجيل | Required | All mandatory fields filled, documents uploaded, T&C checked. | Submits the company registration form. |     | Click. | Clear label, disabled until form valid. |
| COMP-012 | Text Display | Error/Success Message Display | عرض رسالة الخطأ/النجاح | Optional |     | Displays validation, error, or success messages. | Messages/Notifications | Read. | aria-live for dynamic updates. |

Export to Sheets

Please let me know if you would like me to continue detailing the remaining user stories in this format.

continue

B

BA

Custom Gem

Here are more detailed user stories based on the provided BRD, continuing the previous format.

**User Story 4: Publish New Training Opportunity (Company Representative)**

**Main User Story Table**

| Field | Description | Content Guidelines |
| --- | --- | --- |
| **Name** | Publish New Training Opportunity | Process for Company Representatives to create and publish a new training opportunity for trainees. |
| **User Story** | As a Company Representative, I want to publish new training opportunities so that trainees can view and apply for them. |     |
| **Story Points** | 13  | Complex, involves multiple fields, potential attachments, and an approval workflow. |
| **User Roles** | Company Representative | Only approved company representatives. |
| **Access Requirements** | Authenticated as an approved Company Representative. |     |
| **Trigger** | User navigates to "My Training Opportunities" and clicks "Add New Opportunity". |     |
| **Frequency of Use** | Medium | As new opportunities arise. |
| **Pre-condition** | Company profile is approved. User is logged in as Company Representative. |     |
| **Business Rules** | All mandatory fields must be filled. Opportunity must be relevant to the program. Approval by Institution Supervisor is required before publication. | End date must be after start date. |
| **Post-condition** | Training opportunity is submitted for approval or published (if auto-approved). |     |
| **Risk** | Incomplete opportunity details; irrelevant opportunities; delayed approval process. | Implement comprehensive validation, clear approval workflow, provide status updates. |
| **Assumptions** | Representative has all necessary details for the training opportunity. |     |
| **UX/UI Design Link** | \[Link to Figma Mockup for Add Training Opportunity Form\] (Placeholder) |     |

Export to Sheets

**Process Flow Table**

| Step | Action Description | Actor | Related Message Codes | Notes |
| --- | --- | --- | --- | --- |
| 1   | Company Representative logs in and navigates to "My Training Opportunities". | Company Representative |     |     |
| 2   | User clicks "Add New Opportunity" button. | Company Representative |     |     |
| 3   | System displays the "New Training Opportunity" form. | System |     |     |
| 4   | User fills in mandatory details (Title, Description, Dates, Location, Requirements, Benefits, Number of Trainees, etc.). | Company Representative |     |     |
| 5   | User uploads any relevant attachments (e.g., detailed curriculum, company brochure). | Company Representative |     |     |
| 6   | User clicks "Submit for Approval" (or "Publish" if auto-approval). | Company Representative |     |     |
| 7   | System validates all input data and attachments. | System | MSG-OPP-002, MSG-OPP-003, MSG-OPP-004 | If validation fails, errors displayed. |
| 8   | If data is valid, system creates a new Training Opportunity with "Pending Approval" status. | System |     |     |
| 9   | System sends a confirmation to the Company Representative. | System | MSG-OPP-001 |     |
| 10  | System notifies Institution Supervisors for approval. | System |     |     |
| 11  | Institution Supervisor reviews the opportunity details. | Institution Supervisor |     | Manual review. |
| 12  | Institution Supervisor approves or rejects the opportunity. | Institution Supervisor | MSG-OPP-005, MSG-OPP-006 | Notification sent to Company Representative, and opportunity status updated. |

Export to Sheets

**Alternative Flow Table**

| Alternative Scenario | Condition | Action | Related Message Codes | Resolution |
| --- | --- | --- | --- | --- |
| Missing Mandatory Fields | User attempts to submit with required fields empty. | System highlights empty fields and displays an error message. | MSG-OPP-002 | User fills in all mandatory fields. |
| Invalid Date Range | Training end date is before start date. | System displays a validation error. | MSG-OPP-003 | User corrects the date range. |
| Attachment Upload Failure | Attachment is too large or wrong format. | System displays an error message. | MSG-OPP-004 | User resubmits with correct file type/size. |
| Opportunity Rejection | Institution Supervisor rejects the opportunity. | System sends rejection notification with reason. | MSG-OPP-006 | Company Representative can amend and resubmit or contact support. |
| Exceeding Trainee Capacity | User enters a number of trainees exceeding predefined limits (if any). | System displays a validation error. | MSG-OPP-007 | User reduces the number of trainees. |

Export to Sheets

**Acceptance Criteria Table**

| Scenario | Given | When | Then |
| --- | --- | --- | --- |
| Successful Submission | Company Representative provides valid opportunity details and attachments | User clicks "Submit for Approval" | A new training opportunity is created with "Pending Approval" status, and a confirmation is sent. |
| Approval by Supervisor | An opportunity is in "Pending Approval" status | Institution Supervisor approves the opportunity | The opportunity status changes to "Approved" and becomes visible to trainees. |
| Rejection by Supervisor | An opportunity is in "Pending Approval" status | Institution Supervisor rejects the opportunity with a reason | The opportunity status changes to "Rejected", and the representative is notified with the reason. |
| Invalid Start/End Date | Start Date is 2024-01-01, End Date is 2023-12-31 | User clicks "Submit for Approval" | An error message "End date must be after start date" is displayed. |
| Missing Description | User leaves "Opportunity Description" blank | User clicks "Submit for Approval" | An error message "Opportunity Description is required" is displayed. |

Export to Sheets

**Data Entities Table**

Entity Name: Training Opportunity | Attribute (English) | Attribute (Arabic) | Mandatory/Optional | Attribute Type | Data Length | Integration Requirements | Default Value | Condition (if needed) | Rules (if needed) | Sample in Arabic | Sample in English | |---|---|---|---|---|---|---|---|---|---|---| | Opportunity ID | معرف الفرصة | Mandatory | Number | | Database (Auto-generated) | | Unique | Primary Key | 5001 | 5001 | | Company ID | معرف الشركة | Mandatory | Relation to Company Profile | | Database | | | Foreign Key | 98765 | 98765 | | Title (English) | العنوان (الإنجليزية) | Mandatory | Text | 255 | Database | | | Min 10 chars | "Software Development Apprenticeship" | "Software Development Apprenticeship" | | Title (Arabic) | العنوان (العربية) | Mandatory | Text | 255 | Database | | | Min 10 chars | "برنامج تدريب تطوير البرمجيات" | "Software Development Apprenticeship" | | Description (English) | الوصف (الإنجليزية) | Mandatory | Text | 2000 | Database | | | Min 50 chars | "This program aims to develop..." | "This program aims to develop..." | | Description (Arabic) | الوصف (العربية) | Mandatory | Text | 2000 | Database | | | Min 50 chars | "يهدف هذا البرنامج إلى تطوير..." | "This program aims to develop..." | | Start Date | تاريخ البدء | Mandatory | Date | | Database | | | Must be future date | "2025-09-01" | "2025-09-01" | | End Date | تاريخ الانتهاء | Mandatory | Date | | Database | | End Date > Start Date | | "2026-03-01" | "2026-03-01" | | Location | الموقع | Mandatory | Text | 255 | Database | | | Physical address or "Remote" | "Riyadh, Saudi Arabia" | "Riyadh, Saudi Arabia" | | Required Skills | المهارات المطلوبة | Optional | Text (Comma-separated) | 500 | Database | | | | "Java, Python, SQL" | "Java, Python, SQL" | | Benefits | المزايا | Optional | Text | 1000 | Database | | | | "Competitive stipend, mentorship" | "Competitive stipend, mentorship" | | Number of Trainees | عدد المتدربين | Mandatory | Number | | Database | | Min 1 | Max 100 | 5 | 5 | | Application Deadline | آخر موعد للتقديم | Mandatory | Date | | Database | | Before Start Date | | "2025-08-15" | "2025-08-15" | | Opportunity Status | حالة الفرصة | Mandatory | Text (Enum) | 20 | Database | "Pending Approval" | | Enum values: Pending Approval, Approved, Rejected, Closed | "قيد الموافقة" | "Pending Approval" | | Attachments | المرفقات | Optional | File Upload | | File Storage | | Max 3 files, 10MB each | PDF, DOCX | N/A | N/A | | Rejection Reason | سبب الرفض | Optional | Text | 500 | Database | | Only if Opportunity Status is "Rejected" | | "الوصف غير مكتمل" | "Incomplete description" |

**Messages/Notifications Table**

| Message Code | Message (English) | Message (Arabic) | Message Type | Communication Method |
| --- | --- | --- | --- | --- |
| MSG-OPP-001 | Training opportunity submitted successfully for approval. | تم تقديم فرصة التدريب بنجاح للموافقة. | Success | In-App, Email |
| MSG-OPP-002 | All mandatory fields must be filled to submit the training opportunity. | يجب ملء جميع الحقول الإلزامية لتقديم فرصة التدريب. | Validation Error | In-App |
| MSG-OPP-003 | The end date of the training must be after the start date. | يجب أن يكون تاريخ انتهاء التدريب بعد تاريخ البدء. | Validation Error | In-App |
| MSG-OPP-004 | Attachment upload failed. Please ensure files are PDF/DOCX and less than 10MB each. | فشل تحميل المرفقات. يرجى التأكد من أن الملفات بصيغة PDF/DOCX وأقل من 10 ميجابايت لكل منها. | Error | In-App |
| MSG-OPP-005 | Your training opportunity "\[Title\]" has been approved and is now live! | تمت الموافقة على فرصة التدريب الخاصة بك "\[العنوان\]" وهي الآن متاحة! | Success | Email, In-App |
| MSG-OPP-006 | Your training opportunity "\[Title\]" has been rejected due to: \[Rejection Reason\]. | تم رفض فرصة التدريب الخاصة بك "\[العنوان\]" بسبب: \[سبب الرفض\]. | Error | Email, In-App |
| MSG-OPP-007 | The number of trainees exceeds the allowed maximum. Please reduce the number. | عدد المتدربين يتجاوز الحد الأقصى المسموح به. يرجى تقليل العدد. | Validation Error | In-App |

Export to Sheets

**Screen Elements Table**

| Element ID | Element Type | Element Name (English) | Element Name (Arabic) | Required/Optional | Validation Rules | Business Logic | Related Data Entity | User Interaction | Accessibility Notes |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| OPP-001 | Form | New Training Opportunity Form | نموذج فرصة تدريب جديدة | Required |     | Form for creating a new training opportunity. | Training Opportunity | Fill in fields, upload files, click submit. | Clear form labels, logical grouping. |
| OPP-002 | Text Input | Opportunity Title Field (English) | حقل عنوان الفرصة (إنجليزية) | Mandatory | Min 10 characters. | Title of the training opportunity in English. | Title (English) | Type text. | Placeholder, aria-required. |
| OPP-003 | Text Input | Opportunity Title Field (Arabic) | حقل عنوان الفرصة (عربية) | Mandatory | Min 10 characters. | Title of the training opportunity in Arabic. | Title (Arabic) | Type text. | Placeholder, aria-required. |
| OPP-004 | Text Area | Description Field (English) | حقل الوصف (إنجليزية) | Mandatory | Min 50 characters. | Detailed description of the training opportunity in English. | Description (English) | Type text. | Placeholder, character counter. |
| OPP-005 | Text Area | Description Field (Arabic) | حقل الوصف (عربية) | Mandatory | Min 50 characters. | Detailed description of the training opportunity in Arabic. | Description (Arabic) | Type text. | Placeholder, character counter. |
| OPP-006 | Date Picker | Start Date Field | حقل تاريخ البدء | Mandatory | Must be future date. | Start date of the training program. | Start Date | Select date. | Calendar icon, keyboard navigation. |
| OPP-007 | Date Picker | End Date Field | حقل تاريخ الانتهاء | Mandatory | Must be after Start Date. | End date of the training program. | End Date | Select date. | Calendar icon, keyboard navigation. |
| OPP-008 | Text Input | Location Field | حقل الموقع | Mandatory |     | Physical location or "Remote". | Location | Type text or select from dropdown. | Autocomplete suggestions. |
| OPP-009 | Text Input | Required Skills Field | حقل المهارات المطلوبة | Optional |     | Comma-separated list of skills. | Required Skills | Type text. | Tag input suggestions. |
| OPP-010 | Number Input | Number of Trainees Field | حقل عدد المتدربين | Mandatory | Min 1, Max 100. | Maximum number of trainees for this opportunity. | Number of Trainees | Type number. | Stepper controls. |
| OPP-011 | Date Picker | Application Deadline Field | حقل آخر موعد للتقديم | Mandatory | Before Start Date. | Deadline for submitting applications. | Application Deadline | Select date. | Calendar icon, keyboard navigation. |
| OPP-012 | File Upload | Attachments Field | حقل المرفقات | Optional | Max 3 files, 10MB each, PDF/DOCX. | Allows uploading supporting documents. | Attachments | Click to browse and upload. | Clear instructions for file types/sizes. |
| OPP-013 | Button | Submit for Approval Button | زر تقديم للموافقة | Required | All mandatory fields, valid dates/numbers. | Submits the opportunity for review. |     | Click. | Clear label, disabled until form valid. |
| OPP-014 | Text Display | Status Indicator | مؤشر الحالة | Optional |     | Displays current approval status (e.g., Pending Approval). | Opportunity Status | Read. | Visually distinct for status. |
| OPP-015 | Text Area | Rejection Reason Field | حقل سبب الرفض | Optional |     | Displays reason if opportunity is rejected. | Rejection Reason | Read. | Visible only if rejected. |

Export to Sheets

**User Story 5: Apply for Training Opportunity (Trainee)**

**Main User Story Table**

| Field | Description | Content Guidelines |
| --- | --- | --- |
| **Name** | Apply for Training Opportunity | Process for trainees to submit an application for a specific training opportunity. |
| **User Story** | As a Trainee, I want to apply for suitable training opportunities so that I can participate in the apprenticeship program. |     |
| **Story Points** | 8   | Involves form submission, profile data, and attachment uploads. |
| **User Roles** | Trainee | Only registered and logged-in trainees. |
| **Access Requirements** | Authenticated as a Trainee. Opportunity must be "Approved" and open for applications. |     |
| **Trigger** | User views a training opportunity and clicks "Apply Now". |     |
| **Frequency of Use** | High | As trainees find suitable opportunities. |
| **Pre-condition** | Trainee is logged in. Training opportunity is active and accepting applications. |     |
| **Business Rules** | All mandatory application fields must be filled. Trainee can only apply once per opportunity. Application must be submitted before the deadline. |     |
| **Post-condition** | Application is submitted, and trainee receives confirmation. Application status is updated. |     |
| **Risk** | Incomplete applications; missing documents; late submissions. | Implement strong validation, clear deadlines, provide confirmation. |
| **Assumptions** | Trainee has prepared necessary documents (e.g., CV, cover letter). |     |
| **UX/UI Design Link** | \[Link to Figma Mockup for Training Opportunity Application Form\] (Placeholder) |     |

Export to Sheets

**Process Flow Table**

| Step | Action Description | Actor | Related Message Codes | Notes |
| --- | --- | --- | --- | --- |
| 1   | Trainee logs in and browses available training opportunities. | Trainee |     |     |
| 2   | User views a specific opportunity and clicks "Apply Now". | Trainee |     |     |
| 3   | System displays the application form, pre-filling known trainee data. | System |     |     |
| 4   | User reviews pre-filled data, fills in any additional required fields (e.g., cover letter). | Trainee |     |     |
| 5   | User uploads required documents (e.g., CV, academic transcripts). | Trainee |     |     |
| 6   | User clicks "Submit Application" button. | Trainee |     |     |
| 7   | System validates all input data and attachments. | System | MSG-APP-002, MSG-APP-003 | If validation fails, errors displayed. |
| 8   | If valid, system creates a new Application linked to the Trainee and Opportunity. | System |     |     |
| 9   | System sends a confirmation email to the Trainee. | System | MSG-APP-001 |     |
| 10  | System notifies the Company Representative about the new application. | System |     |     |

Export to Sheets

**Alternative Flow Table**

| Alternative Scenario | Condition | Action | Related Message Codes | Resolution |
| --- | --- | --- | --- | --- |
| Already Applied | Trainee tries to apply for an opportunity they already applied to. | System displays an error message "You have already applied for this opportunity." | MSG-APP-002 | User cannot re-apply, can view existing application status. |
| Application Deadline Passed | Trainee tries to apply after the opportunity's deadline. | System disables the "Apply Now" button and displays a message. | MSG-APP-003 | User cannot apply, sees that the deadline has passed. |
| Missing Mandatory Fields | User attempts to submit with required application fields empty. | System highlights empty fields and displays an error message. | MSG-APP-004 | User fills in all mandatory fields. |
| Document Upload Failure | Application attachment is too large or wrong format. | System displays an error message. | MSG-APP-005 | User resubmits with correct file type/size. |

Export to Sheets

**Acceptance Criteria Table**

| Scenario | Given | When | Then |
| --- | --- | --- | --- |
| Successful Application | Trainee provides all mandatory application details and attachments | User clicks "Submit Application" before deadline | A new application is created, application status is "Submitted", and confirmation is sent to the trainee. |
| Rejection of Duplicate Application | Trainee attempts to apply for an opportunity already applied to | User clicks "Apply Now" | An error message "You have already applied" is displayed, and no new application is created. |
| Application After Deadline | Trainee views an opportunity where the deadline has passed | User tries to click "Apply Now" | The "Apply Now" button is disabled or an alert states that applications are closed. |
| Missing CV | User attempts to submit application without uploading CV (if mandatory) | User clicks "Submit Application" | An error message "CV upload is required" is displayed. |

Export to Sheets

**Data Entities Table**

Entity Name: Application | Attribute (English) | Attribute (Arabic) | Mandatory/Optional | Attribute Type | Data Length | Integration Requirements | Default Value | Condition (if needed) | Rules (if needed) | Sample in Arabic | Sample in English | |---|---|---|---|---|---|---|---|---|---|---| | Application ID | معرف الطلب | Mandatory | Number | | Database (Auto-generated) | | Unique | Primary Key | 7001 | 7001 | | Trainee ID | معرف المتدرب | Mandatory | Relation to Trainee Account | | Database | | | Foreign Key | 12345 | 12345 | | Opportunity ID | معرف الفرصة | Mandatory | Relation to Training Opportunity | | Database | | | Foreign Key | 5001 | 5001 | | Application Date | تاريخ التقديم | Mandatory | Date | | Database (Auto-generated) | Current Date | | Read-only | "2025-08-01" | "2025-08-01" | | Status | الحالة | Mandatory | Text (Enum) | 20 | Database | "Submitted" | | Enum values: Submitted, Under Review, Interview, Accepted, Rejected | "تم التقديم" | "Submitted" | | Cover Letter (English) | رسالة التغطية (إنجليزية) | Optional | Text | 1000 | Database | | | | "I am highly interested in..." | "I am highly interested in..." | | Cover Letter (Arabic) | رسالة التغطية (عربية) | Optional | Text | 1000 | Database | | | | "أنا مهتم جداً بـ..." | "I am highly interested in..." | | CV/Resume | السيرة الذاتية | Mandatory | File Upload | | File Storage | | | PDF only, Max 2MB | N/A | N/A | | Academic Transcripts | السجلات الأكاديمية | Optional | File Upload | | File Storage | | | PDF only, Max 2MB | N/A | N/A | | Company Feedback | ملاحظات الشركة | Optional | Text | 1000 | Database | | Only if Status is "Rejected" | | "الخبرة لا تتناسب" | "Experience not suitable" |

**Messages/Notifications Table**

| Message Code | Message (English) | Message (Arabic) | Message Type | Communication Method |
| --- | --- | --- | --- | --- |
| MSG-APP-001 | Your application for "\[Opportunity Title\]" has been submitted successfully! | تم تقديم طلبك لـ "\[عنوان الفرصة\]" بنجاح! | Success | In-App, Email |
| MSG-APP-002 | You have already applied for this training opportunity. | لقد تقدمت بالفعل بطلب لفرصة التدريب هذه. | Error | In-App |
| MSG-APP-003 | The application deadline for this opportunity has passed. | لقد فات موعد تقديم الطلبات لهذه الفرصة. | Warning | In-App |
| MSG-APP-004 | All mandatory fields must be filled to submit your application. | يجب ملء جميع الحقول الإلزامية لتقديم طلبك. | Validation Error | In-App |
| MSG-APP-005 | Document upload failed. Please ensure files are PDF and less than 2MB. | فشل تحميل المستند. يرجى التأكد من أن الملفات بصيغة PDF وأقل من 2 ميجابايت. | Error | In-App |

Export to Sheets

**Screen Elements Table**

| Element ID | Element Type | Element Name (English) | Element Name (Arabic) | Required/Optional | Validation Rules | Business Logic | Related Data Entity | User Interaction | Accessibility Notes |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| APP-001 | Form | Training Opportunity Application Form | نموذج طلب فرصة تدريب | Required |     | Form for submitting an application to a training opportunity. | Application | Fill in fields, upload files, click submit. | Clear form labels, pre-filled data. |
| APP-002 | Text Display | Opportunity Title | عنوان الفرصة | Required |     | Displays the title of the opportunity being applied for. | Training Opportunity (Title) | Read. | Clear heading. |
| APP-003 | Text Area | Cover Letter Field (English) | حقل رسالة التغطية (إنجليزية) | Optional | Max 1000 characters. | Allows trainee to write a personalized cover letter. | Cover Letter (English) | Type text. | Placeholder, character counter. |
| APP-004 | Text Area | Cover Letter Field (Arabic) | حقل رسالة التغطية (عربية) | Optional | Max 1000 characters. | Allows trainee to write a personalized cover letter in Arabic. | Cover Letter (Arabic) | Type text. | Placeholder, character counter. |
| APP-005 | File Upload | CV/Resume Upload | تحميل السيرة الذاتية | Mandatory | PDF only, Max 2MB. | Allows uploading of the trainee's CV/Resume. | CV/Resume | Click to browse and upload. | Clear instructions for file types/sizes. |
| APP-006 | File Upload | Academic Transcripts Upload | تحميل السجلات الأكاديمية | Optional | PDF only, Max 2MB. | Allows uploading of academic transcripts. | Academic Transcripts | Click to browse and upload. | Clear instructions. |
| APP-007 | Button | Submit Application Button | زر تقديم الطلب | Required | All mandatory fields filled, documents uploaded, within deadline. | Submits the application. |     | Click. | Clear label, disabled if not valid or past deadline. |
| APP-008 | Text Display | Application Status Indicator | مؤشر حالة الطلب | Optional |     | Displays the current status of the application (e.g., Submitted, Under Review). | Application (Status) | Read. | Visually distinct for status. |
| APP-009 | Text Display | Deadline Passed Message | رسالة انتهاء الموعد النهائي | Optional |     | Informs user if application deadline has passed. |     |     |     |