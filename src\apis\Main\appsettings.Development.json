{"AllowedOrigins": "http://localhost:4200", "RedisInstanceName": "localhost", "ConnectionStrings": {"default": "Server=./; Database=Jadwa; User=sa; Password=******;MultipleActiveResultSets=true;TrustServerCertificate=true", "Redis": "localhost:6379"}, "jwtSettings": {"secret": "9832yfdfsdkfhskduurc&%*&(&fkdfcweechwlcwjjjskflsdif;aniodskdjfwiokfjs", "issure": "JadwaIdentityProject", "audience": "webSite", "validateIssure": true, "validateAudience": true, "validateLifeTime": true, "validateIssureSigningKey": true, "accessTokenExpireDate": 1, "refreshTokenExpireDate": 30}}